const SystemMonitoringService = require('./server/services/SystemMonitoringService');

console.log('Testing SystemMonitoringService...');

SystemMonitoringService.getSystemHealth()
  .then(result => {
    console.log('System health result:');
    console.log(JSON.stringify(result, null, 2));
    process.exit(0);
  })
  .catch(error => {
    console.error('System health error:', error);
    process.exit(1);
  });
