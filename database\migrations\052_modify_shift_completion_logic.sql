-- Migration: Modify shift completion logic to prevent automatic completion
-- Purpose: Update shift status functions to only handle automatic transitions between 'scheduled' and 'active'
--          Remove automatic completion logic entirely and add manual completion control

-- Drop existing functions to recreate them with modified logic
DROP FUNCTION IF EXISTS evaluate_shift_status CASCADE;
DROP FUNCTION IF EXISTS schedule_auto_activation CASCADE;
DROP FUNCTION IF EXISTS update_all_shift_statuses CASCADE;

-- Create the updated evaluate_shift_status function without automatic completion
CREATE FUNCTION evaluate_shift_status(
    p_shift_id INTEGER,
    p_reference_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
) RETURNS TEXT AS $$
DECLARE
    v_shift_type TEXT;
    v_start_date DATE;
    v_end_date DATE;
    v_start_time TIME;
    v_end_time TIME;
    v_current_status TEXT;
    v_current_date DATE;
    v_current_time TIME;
    v_is_overnight BOOLEAN;
    v_is_within_date_range BOOLEAN;
    v_is_within_time_window BOOLEAN;
BEGIN
    -- Get shift details
    SELECT 
        shift_type,
        start_date,
        end_date,
        start_time,
        end_time,
        status::TEXT,
        start_time > end_time
    INTO
        v_shift_type,
        v_start_date,
        v_end_date,
        v_start_time,
        v_end_time,
        v_current_status,
        v_is_overnight
    FROM driver_shifts
    WHERE id = p_shift_id;
    
    -- If shift not found or cancelled, return error
    IF v_shift_type IS NULL OR v_current_status = 'cancelled' THEN
        RETURN 'error';
    END IF;
    
    -- If shift is already completed, keep it completed (manual completion only)
    IF v_current_status = 'completed' THEN
        RETURN 'completed';
    END IF;
    
    -- Extract date and time from reference timestamp
    v_current_date := p_reference_timestamp::DATE;
    v_current_time := p_reference_timestamp::TIME;
    
    -- Check if current date is within shift date range
    v_is_within_date_range := (v_current_date BETWEEN v_start_date AND v_end_date);
    
    -- Check if current time is within shift time window
    IF v_is_overnight THEN
        -- For overnight shifts (e.g., 10 PM to 6 AM)
        v_is_within_time_window := (v_current_time >= v_start_time OR v_current_time <= v_end_time);
    ELSE
        -- For regular shifts (e.g., 6 AM to 6 PM)
        v_is_within_time_window := (v_current_time BETWEEN v_start_time AND v_end_time);
    END IF;
    
    -- Apply business rules for status determination
    -- Rule 1: Active - within date range AND within time window
    -- Rule 2: Scheduled - within date range BUT outside time window
    -- Rule 3: Completed - REMOVED (now manual only)
    
    IF v_is_within_date_range THEN
        IF v_is_within_time_window THEN
            RETURN 'active';
        ELSE
            RETURN 'scheduled';
        END IF;
    ELSE
        -- If we're past the end date, keep the current status
        -- This prevents automatic completion
        RETURN v_current_status;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create the updated schedule_auto_activation function without automatic completion
CREATE FUNCTION schedule_auto_activation()
RETURNS void AS $$
DECLARE
    shift_record RECORD;
    calculated_status TEXT;
    current_status TEXT;
    activated_count INTEGER := 0;
    scheduled_count INTEGER := 0;
    updated_count INTEGER := 0;
BEGIN
    -- Process all non-cancelled and non-completed shifts
    FOR shift_record IN 
        SELECT id, status FROM driver_shifts 
        WHERE status NOT IN ('cancelled', 'completed')
    LOOP
        current_status := shift_record.status;
        calculated_status := evaluate_shift_status(shift_record.id, CURRENT_TIMESTAMP);
        
        -- Only update if status has changed and is not 'error'
        IF calculated_status != current_status AND calculated_status != 'error' THEN
            -- Update the shift status
            UPDATE driver_shifts 
            SET status = calculated_status::shift_status, 
                updated_at = CURRENT_TIMESTAMP 
            WHERE id = shift_record.id;
            
            updated_count := updated_count + 1;
            
            -- Count by status type
            IF calculated_status = 'active' THEN
                activated_count := activated_count + 1;
            ELSIF calculated_status = 'scheduled' THEN
                scheduled_count := scheduled_count + 1;
            END IF;
        END IF;
    END LOOP;
    
    -- Log the results if any updates were made
    IF updated_count > 0 THEN
        INSERT INTO system_logs (
            log_type, 
            message, 
            details
        ) VALUES (
            'SHIFT_AUTO_ACTIVATION',
            'Auto-activated shifts based on current time',
            jsonb_build_object(
                'updated_count', updated_count,
                'activated_count', activated_count,
                'scheduled_count', scheduled_count,
                'timestamp', CURRENT_TIMESTAMP
            )
        );
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create the updated update_all_shift_statuses function without automatic completion
CREATE FUNCTION update_all_shift_statuses(
    p_reference_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
) RETURNS TABLE (
    updated_count INTEGER,
    activated_count INTEGER,
    scheduled_count INTEGER,
    completed_count INTEGER,
    total_count INTEGER
) AS $$
DECLARE
    v_updated_count INTEGER := 0;
    v_activated_count INTEGER := 0;
    v_scheduled_count INTEGER := 0;
    v_completed_count INTEGER := 0;
    v_total_count INTEGER := 0;
    v_shift RECORD;
    v_calculated_status TEXT;
BEGIN
    -- Count total non-cancelled shifts
    SELECT COUNT(*) INTO v_total_count FROM driver_shifts WHERE status != 'cancelled';
    
    -- Count already completed shifts
    SELECT COUNT(*) INTO v_completed_count FROM driver_shifts WHERE status = 'completed';
    
    -- Process all non-cancelled and non-completed shifts
    FOR v_shift IN 
        SELECT id, status::TEXT FROM driver_shifts 
        WHERE status NOT IN ('cancelled', 'completed')
    LOOP
        -- Calculate the correct status
        v_calculated_status := evaluate_shift_status(v_shift.id, p_reference_timestamp);
        
        -- Only update if status has changed and is not 'error'
        IF v_calculated_status != v_shift.status AND v_calculated_status != 'error' THEN
            -- Update the shift status
            UPDATE driver_shifts 
            SET status = v_calculated_status::shift_status, 
                updated_at = p_reference_timestamp
            WHERE id = v_shift.id;
            
            v_updated_count := v_updated_count + 1;
            
            -- Count by status type
            IF v_calculated_status = 'active' THEN
                v_activated_count := v_activated_count + 1;
            ELSIF v_calculated_status = 'scheduled' THEN
                v_scheduled_count := v_scheduled_count + 1;
            END IF;
        END IF;
    END LOOP;
    
    -- Return the statistics
    updated_count := v_updated_count;
    activated_count := v_activated_count;
    scheduled_count := v_scheduled_count;
    completed_count := v_completed_count;
    total_count := v_total_count;
    
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql;

-- Add function comments
COMMENT ON FUNCTION evaluate_shift_status(INTEGER, TIMESTAMP WITH TIME ZONE) IS 
'Evaluates the correct status for a shift based on date/time rules with proper overnight logic. Does not automatically complete shifts.';

COMMENT ON FUNCTION schedule_auto_activation() IS 
'Auto-activates shifts based on current time. Does not automatically complete shifts.';

COMMENT ON FUNCTION update_all_shift_statuses(TIMESTAMP WITH TIME ZONE) IS 
'Updates shift statuses between scheduled and active only. Does not automatically complete shifts.';