const CleanupService = require('./services/CleanupService');

// Configuration constants
const TEST_CONFIG = {
    displayLimit: 5,
    separatorLength: 50,
    showStackTrace: process.env.NODE_ENV === 'development'
};

// Helper function to display function lists
function displayFunctionList(functions, title, limit = TEST_CONFIG.displayLimit) {
    if (functions.length === 0) return;
    
    console.log(`\n${title}:`);
    functions.slice(0, limit).forEach(func => {
        console.log(`   - ${func.name} (${func.type}) in ${func.file}:${func.line}`);
    });
    
    if (functions.length > limit) {
        console.log(`   ... and ${functions.length - limit} more`);
    }
}

// Helper function to validate analysis results
function validateAnalysisResult(analysis) {
    const required = ['totalFiles', 'analyzedFunctions', 'criticalFunctions', 'unusedFunctions', 'safeToRemove'];
    
    for (const field of required) {
        if (analysis[field] === undefined) {
            throw new Error(`Invalid analysis result: missing ${field}`);
        }
    }
    
    if (!Array.isArray(analysis.safeToRemove)) {
        throw new Error('Invalid analysis result: safeToRemove must be an array');
    }
}

// Helper function to generate summary report
function generateSummaryReport(analysis, integrity) {
    console.log('\n📋 CLEANUP ANALYSIS SUMMARY:');
    console.log('='.repeat(TEST_CONFIG.separatorLength));
    console.log(`Total JavaScript files scanned: ${analysis.totalFiles}`);
    console.log(`Total functions analyzed: ${analysis.analyzedFunctions.length}`);
    console.log(`Critical functions (preserved): ${analysis.criticalFunctions.length}`);
    console.log(`Unused functions detected: ${analysis.unusedFunctions.length}`);
    console.log(`Functions safe to remove: ${analysis.safeToRemove.length}`);
    console.log(`System integrity: ${integrity.passed ? 'PASSED' : 'FAILED'}`);
    
    // Generate recommendation
    if (analysis.safeToRemove.length > 0) {
        console.log('\n⚠️  RECOMMENDATION:');
        console.log('   Functions have been identified that appear to be unused.');
        console.log('   Review the analysis results before executing cleanup.');
        console.log('   Always create a backup before removing any code.');
    } else {
        console.log('\n✅ RECOMMENDATION:');
        console.log('   No unused functions detected. Codebase appears clean.');
    }
}

// Helper function to run individual tests with error handling
async function runTest(testName, testFunction) {
    try {
        console.log(`\n${testName}...`);
        const result = await testFunction();
        console.log(`✅ ${testName.replace(/.*: /, '')} completed`);
        return { success: true, result };
    } catch (error) {
        console.error(`❌ ${testName.replace(/.*: /, '')} failed:`, error.message);
        return { success: false, error };
    }
}

async function testCleanupService() {
    console.log('🧹 Testing CleanupService...\n');
    
    const cleanupService = new CleanupService();
    let analysis, integrity;
    
    // Run tests with improved error handling
    const testResults = [];
    
    // Test 1: Analyze unused functions
    const test1Result = await runTest('📊 Test 1: Analyzing unused functions', async () => {
        analysis = await cleanupService.analyzeUnusedFunctions();
        validateAnalysisResult(analysis);
        
        console.log(`✅ Analysis completed:`);
        console.log(`   - Total files analyzed: ${analysis.totalFiles}`);
        console.log(`   - Total functions found: ${analysis.analyzedFunctions.length}`);
        console.log(`   - Critical functions: ${analysis.criticalFunctions.length}`);
        console.log(`   - Unused functions: ${analysis.unusedFunctions.length}`);
        console.log(`   - Safe to remove: ${analysis.safeToRemove.length}`);
        
        displayFunctionList(analysis.safeToRemove, '🔍 Functions safe to remove');
        displayFunctionList(analysis.criticalFunctions, '🛡️ Critical functions (preserved)');
        
        return analysis;
    });
    testResults.push(test1Result);
    
    // Test 2: Verify system integrity
    const test2Result = await runTest('🔍 Test 2: Verifying system integrity', async () => {
        integrity = await cleanupService.verifySystemIntegrity();
        
        if (integrity.passed) {
            console.log('✅ System integrity check passed');
        } else {
            console.log('❌ System integrity check failed:');
            integrity.errors.forEach(error => {
                console.log(`   - ${error.file}: ${error.error}`);
            });
        }
        
        return integrity;
    });
    testResults.push(test2Result);
    
    // Test 3: Create backup (dry run)
    const test3Result = await runTest('💾 Test 3: Testing backup creation', async () => {
        const backupPath = await cleanupService.createBackup();
        console.log(`✅ Backup created successfully at: ${backupPath}`);
        return backupPath;
    });
    testResults.push(test3Result);
    
    // Test 4: Simulate cleanup execution (without actually removing functions)
    const test4Result = await runTest('🧪 Test 4: Simulating cleanup execution', async () => {
        if (analysis && analysis.safeToRemove.length > 0) {
            // Take only first function for simulation
            const testFunction = analysis.safeToRemove[0];
            console.log(`   Simulating removal of: ${testFunction.name} from ${testFunction.file}`);
            console.log('   (This is a simulation - no actual changes made)');
            return { simulated: true, function: testFunction };
        } else {
            console.log('   No functions identified as safe to remove');
            return { simulated: false, reason: 'No unused functions found' };
        }
    });
    testResults.push(test4Result);
    
    // Generate final report
    const successfulTests = testResults.filter(result => result.success).length;
    const totalTests = testResults.length;
    
    console.log(`\n🎉 CleanupService testing completed: ${successfulTests}/${totalTests} tests passed`);
    
    if (analysis && integrity) {
        generateSummaryReport(analysis, integrity);
    }
    
    // Handle any test failures
    const failedTests = testResults.filter(result => !result.success);
    if (failedTests.length > 0) {
        console.log('\n⚠️  Some tests failed:');
        failedTests.forEach((result, index) => {
            console.log(`   ${index + 1}. ${result.error.message}`);
        });
        
        if (TEST_CONFIG.showStackTrace) {
            console.log('\nStack traces:');
            failedTests.forEach((result, index) => {
                console.log(`\n${index + 1}. ${result.error.stack}`);
            });
        }
        
        process.exit(1);
    }
}

// Run the test
if (require.main === module) {
    testCleanupService();
}

module.exports = testCleanupService;