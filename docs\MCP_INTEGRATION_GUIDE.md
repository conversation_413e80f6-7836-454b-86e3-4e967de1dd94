# Model Context Protocol (MCP) Integration Guide

The Hauling QR Trip Management System includes comprehensive Model Context Protocol (MCP) integration to enhance development capabilities and provide advanced AI-powered features.

## 🔌 Overview

MCP enables seamless integration with various AI tools and services, providing enhanced development workflows, data processing capabilities, and intelligent automation features.

## 📋 Configured MCP Servers

### 1. Fetch MCP Server
**Purpose:** Web content fetching and processing
- **Command:** `node C:\Users\<USER>\Documents\Cline\MCP\fetch-mcp\dist\index.js`
- **Auto-approved tools:**
  - `fetch_html` - Fetch HTML content from URLs
  - `fetch_markdown` - Convert web pages to Markdown
  - `fetch_txt` - Extract plain text from web pages
  - `fetch_json` - Fetch and parse JSON data

**Use Cases:**
- Documentation scraping and processing
- API endpoint testing and validation
- Content migration and analysis
- External data integration

### 2. Memory Server
**Purpose:** Knowledge graph and persistent memory management
- **Command:** `@modelcontextprotocol/server-memory`
- **Auto-approved tools:**
  - `create_entities` - Create knowledge entities
  - `create_relations` - Establish entity relationships
  - `add_observations` - Add contextual observations
  - `delete_entities` - Remove entities
  - `delete_observations` - Remove observations
  - `delete_relations` - Remove relationships
  - `read_graph` - Read entire knowledge graph
  - `search_nodes` - Search knowledge nodes
  - `open_nodes` - Access specific nodes

**Use Cases:**
- Project knowledge management
- Code relationship mapping
- Development context preservation
- Team knowledge sharing

### 3. Filesystem Server
**Purpose:** Advanced file system operations
- **Command:** `@modelcontextprotocol/server-filesystem`
- **Allowed directories:**
  - `C:\Users\<USER>\Documents\Cline\MCP\filesystem-server`
  - `C:\Users\<USER>\AppData`
  - `C:\Users\<USER>\Documents`
  - `C:\Users\<USER>