const CleanupService = require('./services/CleanupService');

async function simpleTest() {
    console.log('🧹 Simple CleanupService Test\n');
    
    try {
        const cleanupService = new CleanupService();
        
        // Test 1: Basic analysis
        console.log('📊 Running basic analysis...');
        const analysis = await cleanupService.analyzeCodebase();
        
        if (analysis.success) {
            console.log('✅ Analysis completed successfully');
            console.log(`   - Files analyzed: ${analysis.stats.totalFiles}`);
            console.log(`   - Functions found: ${analysis.stats.totalFunctions}`);
            console.log(`   - Unused functions: ${analysis.stats.unusedFunctions}`);
            console.log(`   - Safe to remove: ${analysis.stats.safeToRemove}`);
        } else {
            console.log('❌ Analysis failed:', analysis.error);
        }
        
        // Test 2: System integrity
        console.log('\n🔍 Checking system integrity...');
        const integrity = await cleanupService.verifySystemIntegrity();
        
        if (integrity.success) {
            console.log('✅ System integrity check passed');
        } else {
            console.log('❌ System integrity issues found');
            if (integrity.missingFiles && integrity.missingFiles.length > 0) {
                console.log('   Missing files:', integrity.missingFiles);
            }
            if (integrity.serverRequireError) {
                console.log('   Server error:', integrity.serverRequireError);
            }
            if (integrity.dbConnectError) {
                console.log('   Database error:', integrity.dbConnectError);
            }
        }
        
        console.log('\n✅ CleanupService basic functionality verified');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    }
}

simpleTest();