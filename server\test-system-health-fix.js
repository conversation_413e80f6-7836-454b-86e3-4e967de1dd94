/**
 * Test System Health Fix Endpoints
 * 
 * Tests the automated fix endpoints to ensure they're working correctly
 * and no longer returning 500 errors.
 */

const axios = require('axios');
require('dotenv').config();

// Configuration
const BASE_URL = process.env.BACKEND_URL || 'http://localhost:5444';
const API_BASE = `${BASE_URL}/api`;

// Test credentials (admin user)
const TEST_CREDENTIALS = {
  email: process.env.ADMIN_EMAIL || '<EMAIL>',
  password: process.env.ADMIN_PASSWORD || 'AdminPassword123'
};

let authToken = null;

/**
 * Authenticate and get token
 */
async function authenticate() {
  try {
    console.log('🔐 Authenticating...');
    const response = await axios.post(`${API_BASE}/auth/login`, TEST_CREDENTIALS);
    
    if (response.data.success && response.data.token) {
      authToken = response.data.token;
      console.log('✅ Authentication successful');
      return true;
    } else {
      console.error('❌ Authentication failed:', response.data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Authentication error:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Create axios instance with auth header
 */
function createAuthenticatedClient() {
  return axios.create({
    baseURL: API_BASE,
    headers: {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    },
    timeout: 30000
  });
}

/**
 * Test system health status endpoint
 */
async function testHealthStatus() {
  try {
    console.log('\n📊 Testing GET /api/system-health/status');
    const client = createAuthenticatedClient();
    const response = await client.get('/system-health/status');
    
    console.log(`✅ Status: ${response.status}`);
    console.log(`✅ Response structure:`, {
      hasOverall: !!response.data.overall,
      hasShifts: !!response.data.shifts,
      hasAssignments: !!response.data.assignments,
      hasTrips: !!response.data.trips,
      hasDatabase: !!response.data.database
    });
    
    return true;
  } catch (error) {
    console.error('❌ Health status test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Test shift fix endpoint
 */
async function testShiftFix() {
  try {
    console.log('\n🔧 Testing POST /api/system-health/fix-shifts');
    const client = createAuthenticatedClient();
    const response = await client.post('/system-health/fix-shifts');
    
    console.log(`✅ Status: ${response.status}`);
    console.log(`✅ Success: ${response.data.success}`);
    console.log(`✅ Message: ${response.data.message}`);
    
    if (response.data.data) {
      console.log(`✅ Fixes applied: ${response.data.data.fixesApplied?.length || 0}`);
      console.log(`✅ Summary:`, response.data.data.summary);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Shift fix test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Test assignment fix endpoint
 */
async function testAssignmentFix() {
  try {
    console.log('\n🔧 Testing POST /api/system-health/fix-assignments');
    const client = createAuthenticatedClient();
    const response = await client.post('/system-health/fix-assignments');
    
    console.log(`✅ Status: ${response.status}`);
    console.log(`✅ Success: ${response.data.success}`);
    console.log(`✅ Message: ${response.data.message}`);
    
    if (response.data.data) {
      console.log(`✅ Fixes applied: ${response.data.data.fixesApplied?.length || 0}`);
      console.log(`✅ Summary:`, response.data.data.summary);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Assignment fix test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Test trip fix endpoint
 */
async function testTripFix() {
  try {
    console.log('\n🔧 Testing POST /api/system-health/fix-trips');
    const client = createAuthenticatedClient();
    const response = await client.post('/system-health/fix-trips');
    
    console.log(`✅ Status: ${response.status}`);
    console.log(`✅ Success: ${response.data.success}`);
    console.log(`✅ Message: ${response.data.message}`);
    
    if (response.data.data) {
      console.log(`✅ Fixes applied: ${response.data.data.fixesApplied?.length || 0}`);
      console.log(`✅ Summary:`, response.data.data.summary);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Trip fix test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🧪 Starting System Health Fix Endpoint Tests');
  console.log(`🌐 Base URL: ${BASE_URL}`);
  
  // Authenticate first
  const authSuccess = await authenticate();
  if (!authSuccess) {
    console.error('❌ Cannot proceed without authentication');
    process.exit(1);
  }
  
  // Run all tests
  const tests = [
    { name: 'Health Status', fn: testHealthStatus },
    { name: 'Shift Fix', fn: testShiftFix },
    { name: 'Assignment Fix', fn: testAssignmentFix },
    { name: 'Trip Fix', fn: testTripFix }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const success = await test.fn();
      if (success) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.error(`❌ Test ${test.name} threw an error:`, error.message);
      failed++;
    }
  }
  
  // Summary
  console.log('\n📋 Test Summary:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${tests.length}`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! System Health Fix endpoints are working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the error messages above.');
  }
  
  process.exit(failed === 0 ? 0 : 1);
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('❌ Test runner error:', error);
    process.exit(1);
  });
}

module.exports = {
  runTests,
  testHealthStatus,
  testShiftFix,
  testAssignmentFix,
  testTripFix
};
