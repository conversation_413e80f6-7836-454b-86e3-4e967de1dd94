import React, { useState, useEffect, useRef, useCallback } from 'react';
import { toast } from 'react-hot-toast';

const AppearanceSettings = () => {
  const canvasRef = useRef(null);
  const fileInputRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [settings, setSettings] = useState({
    logo: {
      src: '',
      alt: 'Hauling QR System',
      width: 40,
      height: 40
    },
    fonts: {
      header: {
        family: 'Inter, system-ui, sans-serif',
        size: '24px',
        weight: '600'
      },
      content: {
        family: 'Inter, system-ui, sans-serif',
        size: '16px',
        weight: '400'
      },
      footer: {
        family: 'Inter, system-ui, sans-serif',
        size: '14px',
        weight: '400'
      }
    }
  });

  const [previewMode, setPreviewMode] = useState(false);

  // Font family options
  const fontFamilies = [
    { value: 'Inter, system-ui, sans-serif', label: 'Inter (Default)' },
    { value: 'Arial, sans-serif', label: 'Arial' },
    { value: 'Helvetica, sans-serif', label: 'Helvetica' },
    { value: 'Georgia, serif', label: 'Georgia' },
    { value: 'Times New Roman, serif', label: 'Times New Roman' },
    { value: 'Courier New, monospace', label: 'Courier New' },
    { value: 'Verdana, sans-serif', label: 'Verdana' },
    { value: 'Trebuchet MS, sans-serif', label: 'Trebuchet MS' }
  ];

  // Font size options
  const fontSizes = {
    header: ['18px', '20px', '22px', '24px', '26px', '28px', '30px', '32px'],
    content: ['12px', '14px', '16px', '18px', '20px', '22px'],
    footer: ['10px', '12px', '14px', '16px', '18px']
  };

  // Font weight options
  const fontWeights = [
    { value: '300', label: 'Light' },
    { value: '400', label: 'Normal' },
    { value: '500', label: 'Medium' },
    { value: '600', label: 'Semi Bold' },
    { value: '700', label: 'Bold' }
  ];

  // Load settings from localStorage on component mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('hauling_appearance_settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Error loading appearance settings:', error);
      }
    }
  }, []);

  // Apply settings to document
  useEffect(() => {
    if (previewMode) {
      applySettingsToDocument();
    }
    return () => {
      if (previewMode) {
        removeSettingsFromDocument();
      }
    };
  }, [settings, previewMode]);

  const applySettingsToDocument = () => {
    const root = document.documentElement;
    
    // Apply font settings
    root.style.setProperty('--font-header-family', settings.fonts.header.family);
    root.style.setProperty('--font-header-size', settings.fonts.header.size);
    root.style.setProperty('--font-header-weight', settings.fonts.header.weight);
    
    root.style.setProperty('--font-content-family', settings.fonts.content.family);
    root.style.setProperty('--font-content-size', settings.fonts.content.size);
    root.style.setProperty('--font-content-weight', settings.fonts.content.weight);
    
    root.style.setProperty('--font-footer-family', settings.fonts.footer.family);
    root.style.setProperty('--font-footer-size', settings.fonts.footer.size);
    root.style.setProperty('--font-footer-weight', settings.fonts.footer.weight);

    // Apply logo settings
    root.style.setProperty('--logo-width', `${settings.logo.width}px`);
    root.style.setProperty('--logo-height', `${settings.logo.height}px`);
  };

  const removeSettingsFromDocument = () => {
    const root = document.documentElement;
    const properties = [
      '--font-header-family', '--font-header-size', '--font-header-weight',
      '--font-content-family', '--font-content-size', '--font-content-weight',
      '--font-footer-family', '--font-footer-size', '--font-footer-weight',
      '--logo-width', '--logo-height'
    ];
    
    properties.forEach(prop => root.style.removeProperty(prop));
  };

  const handleLogoChange = (field, value) => {
    setSettings(prev => ({
      ...prev,
      logo: {
        ...prev.logo,
        [field]: value
      }
    }));
  };

  const handleFontChange = (section, field, value) => {
    setSettings(prev => ({
      ...prev,
      fonts: {
        ...prev.fonts,
        [section]: {
          ...prev.fonts[section],
          [field]: value
        }
      }
    }));
  };

  const handleSave = () => {
    try {
      localStorage.setItem('hauling_appearance_settings', JSON.stringify(settings));
      applySettingsToDocument();
      
      // Trigger storage event for other components to update
      window.dispatchEvent(new StorageEvent('storage', {
        key: 'hauling_appearance_settings',
        newValue: JSON.stringify(settings)
      }));
      
      toast.success('Appearance settings saved successfully!');
    } catch (error) {
      console.error('Error saving appearance settings:', error);
      toast.error('Failed to save appearance settings');
    }
  };

  const handleReset = () => {
    const defaultSettings = {
      logo: {
        src: '',
        alt: 'Hauling QR System',
        width: 40,
        height: 40
      },
      fonts: {
        header: {
          family: 'Inter, system-ui, sans-serif',
          size: '24px',
          weight: '600'
        },
        content: {
          family: 'Inter, system-ui, sans-serif',
          size: '16px',
          weight: '400'
        },
        footer: {
          family: 'Inter, system-ui, sans-serif',
          size: '14px',
          weight: '400'
        }
      }
    };
    
    setSettings(defaultSettings);
    localStorage.removeItem('hauling_appearance_settings');
    removeSettingsFromDocument();
    toast.success('Settings reset to defaults');
  };

  const togglePreview = () => {
    setPreviewMode(!previewMode);
    if (!previewMode) {
      toast.success('Preview mode enabled - changes will be applied temporarily');
    } else {
      toast.success('Preview mode disabled');
    }
  };
  
  // Handle file upload for logo
  const handleLogoFileUpload = (file) => {
    if (!file) return;
    
    // Check file type
    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      toast.error('Please upload a valid image file (JPEG, PNG, GIF, SVG, or WebP)');
      return;
    }
    
    // Check file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('Image file size should be less than 2MB');
      return;
    }
    
    // Create a FileReader to read the file as data URL
    const reader = new FileReader();
    reader.onload = (e) => {
      // Update the logo source with the data URL
      handleLogoChange('src', e.target.result);
      
      // Get image dimensions to set width and height properly
      const img = new Image();
      img.onload = () => {
        // Calculate dimensions while maintaining aspect ratio and ensuring they're within bounds
        const maxWidth = 100; // Maximum width for header display
        const maxHeight = 60; // Maximum height for header display
        
        // Calculate scaled dimensions while preserving aspect ratio
        let width, height;
        const aspectRatio = img.width / img.height;
        
        if (aspectRatio > 1) {
          // Image is wider than tall
          width = Math.min(img.width, maxWidth);
          height = Math.round(width / aspectRatio);
          
          // If height is still too large, scale down further
          if (height > maxHeight) {
            height = maxHeight;
            width = Math.round(height * aspectRatio);
          }
        } else {
          // Image is taller than wide or square
          height = Math.min(img.height, maxHeight);
          width = Math.round(height * aspectRatio);
          
          // If width is still too large, scale down further
          if (width > maxWidth) {
            width = maxWidth;
            height = Math.round(width / aspectRatio);
          }
        }
        
        // Ensure minimum dimensions
        width = Math.max(width, 20);
        height = Math.max(height, 20);
        
        // Update logo dimensions
        handleLogoChange('width', width);
        handleLogoChange('height', height);
        
        toast.success('Logo uploaded successfully!');
      };
      img.src = e.target.result;
    };
    reader.onerror = () => {
      toast.error('Failed to read the image file');
    };
    reader.readAsDataURL(file);
  };
  
  // Handle drag and drop events
  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };
  
  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragging(false);
  };
  
  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleLogoFileUpload(e.dataTransfer.files[0]);
    }
  };
  
  // Function to trigger file input click
  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };
  
  // Function to generate and download favicon from logo
  const generateFavicon = (size) => {
    if (!settings.logo.src) {
      toast.error('Please upload a logo image first');
      return;
    }
    
    // Create a canvas element for drawing the favicon
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');
    
    // Create an image element to load the logo
    const img = new Image();
    img.crossOrigin = 'anonymous'; // Enable CORS for the image
    
    img.onload = () => {
      // Clear the canvas
      ctx.clearRect(0, 0, size, size);
      
      // Draw the image centered and scaled to fit
      const aspectRatio = img.width / img.height;
      let drawWidth, drawHeight, offsetX, offsetY;
      
      if (aspectRatio > 1) {
        // Image is wider than tall
        drawWidth = size;
        drawHeight = size / aspectRatio;
        offsetX = 0;
        offsetY = (size - drawHeight) / 2;
      } else {
        // Image is taller than wide or square
        drawHeight = size;
        drawWidth = size * aspectRatio;
        offsetX = (size - drawWidth) / 2;
        offsetY = 0;
      }
      
      // Draw the image
      ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);
      
      // Convert canvas to blob and download
      canvas.toBlob((blob) => {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `favicon-${size}x${size}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        
        toast.success(`Favicon ${size}x${size} downloaded successfully!`);
      }, 'image/png');
    };
    
    img.onerror = () => {
      toast.error('Failed to load the logo image. Check the URL and try again.');
    };
    
    // Start loading the image
    img.src = settings.logo.src;
  };

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-secondary-900">🎨 Appearance Settings</h2>
          <p className="text-secondary-600 mt-1">Customize the look and feel of your application</p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={togglePreview}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              previewMode 
                ? 'bg-blue-100 text-blue-700 border border-blue-300' 
                : 'bg-secondary-100 text-secondary-700 border border-secondary-300'
            }`}
          >
            {previewMode ? '👁️ Preview On' : '👁️ Preview Off'}
          </button>
        </div>
      </div>

      {/* Logo Settings */}
      <div className="bg-white rounded-lg border border-secondary-200 p-6">
        <h3 className="text-lg font-semibold text-secondary-900 mb-4">🖼️ Logo Settings</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-2">
                Logo Image
              </label>
              <div 
                className={`border-2 ${isDragging ? 'border-blue-400 bg-blue-50' : 'border-dashed border-secondary-300'} rounded-md p-4 text-center cursor-pointer`}
                onClick={triggerFileInput}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => {
                    if (e.target.files && e.target.files[0]) {
                      handleLogoFileUpload(e.target.files[0]);
                    }
                  }}
                />
                <div className="flex flex-col items-center justify-center py-3">
                  <svg className="w-10 h-10 text-secondary-400 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                  <p className="text-sm text-secondary-600 mb-1">
                    {settings.logo.src ? 'Replace logo' : 'Upload logo'}
                  </p>
                  <p className="text-xs text-secondary-500">
                    Click or drag and drop
                  </p>
                  <p className="text-xs text-secondary-400 mt-1">
                    PNG, JPG, GIF, SVG, WebP (max 2MB)
                  </p>
                </div>
              </div>
              {settings.logo.src && (
                <div className="flex items-center justify-between mt-2">
                  <span className="text-xs text-secondary-500">
                    Logo uploaded successfully
                  </span>
                  <button
                    onClick={() => handleLogoChange('src', '')}
                    className="text-xs text-red-500 hover:text-red-700"
                  >
                    Remove
                  </button>
                </div>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-2">
                Alt Text
              </label>
              <input
                type="text"
                value={settings.logo.alt}
                onChange={(e) => handleLogoChange('alt', e.target.value)}
                className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Width (px)
                </label>
                <input
                  type="number"
                  value={settings.logo.width}
                  onChange={(e) => handleLogoChange('width', parseInt(e.target.value) || 40)}
                  min="20"
                  max="200"
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Height (px)
                </label>
                <input
                  type="number"
                  value={settings.logo.height}
                  onChange={(e) => handleLogoChange('height', parseInt(e.target.value) || 40)}
                  min="20"
                  max="200"
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            {/* Favicon Generation */}
            <div className="mt-4 pt-4 border-t border-secondary-200">
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-secondary-700">
                  Generate Favicon
                </label>
                <div className="text-xs text-secondary-500">
                  From your logo
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => generateFavicon(16)}
                  disabled={!settings.logo.src}
                  className={`px-3 py-1 text-xs rounded ${
                    settings.logo.src 
                      ? 'bg-blue-600 text-white hover:bg-blue-700' 
                      : 'bg-secondary-200 text-secondary-400 cursor-not-allowed'
                  }`}
                >
                  16×16
                </button>
                <button
                  onClick={() => generateFavicon(32)}
                  disabled={!settings.logo.src}
                  className={`px-3 py-1 text-xs rounded ${
                    settings.logo.src 
                      ? 'bg-blue-600 text-white hover:bg-blue-700' 
                      : 'bg-secondary-200 text-secondary-400 cursor-not-allowed'
                  }`}
                >
                  32×32
                </button>
                <button
                  onClick={() => generateFavicon(48)}
                  disabled={!settings.logo.src}
                  className={`px-3 py-1 text-xs rounded ${
                    settings.logo.src 
                      ? 'bg-blue-600 text-white hover:bg-blue-700' 
                      : 'bg-secondary-200 text-secondary-400 cursor-not-allowed'
                  }`}
                >
                  48×48
                </button>
              </div>
              <p className="text-xs text-secondary-500 mt-2">
                Click a size to generate and download a favicon for your site
              </p>
            </div>
          </div>
          
          {/* Logo Preview */}
          <div className="flex flex-col items-center justify-center p-6 bg-secondary-50 rounded-lg border-2 border-dashed border-secondary-300">
            <div className="flex-1 flex items-center justify-center w-full">
              {settings.logo.src ? (
                <div className="border border-secondary-200 p-4 rounded-md bg-white">
                  <img
                    src={settings.logo.src}
                    alt={settings.logo.alt}
                    className="object-contain max-w-full max-h-[120px]"
                    style={{
                      maxWidth: '100%',
                      maxHeight: '120px'
                    }}
                    onError={(e) => {
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'block';
                    }}
                  />
                </div>
              ) : (
                <div className="text-center text-secondary-500">
                  <div className="text-4xl mb-2">🖼️</div>
                  <p className="text-sm">Logo Preview</p>
                  <p className="text-xs">Upload an image to see preview</p>
                </div>
              )}
              <div style={{ display: 'none' }} className="text-center text-red-500">
                <div className="text-4xl mb-2">❌</div>
                <p className="text-sm">Invalid Image</p>
              </div>
            </div>
            
            {/* Header Preview */}
            {settings.logo.src && (
              <div className="mt-6 pt-4 border-t border-secondary-200 w-full">
                <p className="text-xs text-secondary-500 mb-2">Header Preview:</p>
                <div className="bg-white p-3 rounded-lg shadow-sm flex items-center space-x-3">
                  <div className="flex-shrink-0 w-10 h-10 flex items-center justify-center overflow-hidden">
                    <img
                      src={settings.logo.src}
                      alt={settings.logo.alt}
                      className="object-contain max-w-full max-h-full"
                      style={{
                        maxWidth: '100%',
                        maxHeight: '100%'
                      }}
                    />
                  </div>
                  <h3 className="text-lg font-semibold text-secondary-800">
                    Hauling QR
                  </h3>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>    
  {/* Font Settings */}
      <div className="bg-white rounded-lg border border-secondary-200 p-6">
        <h3 className="text-lg font-semibold text-secondary-900 mb-4">🔤 Font Settings</h3>
        
        <div className="space-y-6">
          {/* Header Fonts */}
          <div className="border-b border-secondary-200 pb-6">
            <h4 className="text-md font-medium text-secondary-800 mb-4">Header Fonts</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Font Family
                </label>
                <select
                  value={settings.fonts.header.family}
                  onChange={(e) => handleFontChange('header', 'family', e.target.value)}
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {fontFamilies.map(font => (
                    <option key={font.value} value={font.value}>{font.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Font Size
                </label>
                <select
                  value={settings.fonts.header.size}
                  onChange={(e) => handleFontChange('header', 'size', e.target.value)}
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {fontSizes.header.map(size => (
                    <option key={size} value={size}>{size}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Font Weight
                </label>
                <select
                  value={settings.fonts.header.weight}
                  onChange={(e) => handleFontChange('header', 'weight', e.target.value)}
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {fontWeights.map(weight => (
                    <option key={weight.value} value={weight.value}>{weight.label}</option>
                  ))}
                </select>
              </div>
            </div>
            <div className="mt-4 p-4 bg-secondary-50 rounded-lg">
              <p 
                className="text-secondary-900"
                style={{
                  fontFamily: settings.fonts.header.family,
                  fontSize: settings.fonts.header.size,
                  fontWeight: settings.fonts.header.weight
                }}
              >
                Sample Header Text - 🚛 Hauling QR System
              </p>
            </div>
          </div>

          {/* Content Fonts */}
          <div className="border-b border-secondary-200 pb-6">
            <h4 className="text-md font-medium text-secondary-800 mb-4">Content Fonts</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Font Family
                </label>
                <select
                  value={settings.fonts.content.family}
                  onChange={(e) => handleFontChange('content', 'family', e.target.value)}
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {fontFamilies.map(font => (
                    <option key={font.value} value={font.value}>{font.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Font Size
                </label>
                <select
                  value={settings.fonts.content.size}
                  onChange={(e) => handleFontChange('content', 'size', e.target.value)}
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {fontSizes.content.map(size => (
                    <option key={size} value={size}>{size}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Font Weight
                </label>
                <select
                  value={settings.fonts.content.weight}
                  onChange={(e) => handleFontChange('content', 'weight', e.target.value)}
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {fontWeights.map(weight => (
                    <option key={weight.value} value={weight.value}>{weight.label}</option>
                  ))}
                </select>
              </div>
            </div>
            <div className="mt-4 p-4 bg-secondary-50 rounded-lg">
              <p 
                className="text-secondary-900"
                style={{
                  fontFamily: settings.fonts.content.family,
                  fontSize: settings.fonts.content.size,
                  fontWeight: settings.fonts.content.weight
                }}
              >
                Sample content text - This is how your main content will appear throughout the application. It includes regular paragraphs, descriptions, and body text.
              </p>
            </div>
          </div>

          {/* Footer Fonts */}
          <div>
            <h4 className="text-md font-medium text-secondary-800 mb-4">Footer Fonts</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Font Family
                </label>
                <select
                  value={settings.fonts.footer.family}
                  onChange={(e) => handleFontChange('footer', 'family', e.target.value)}
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {fontFamilies.map(font => (
                    <option key={font.value} value={font.value}>{font.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Font Size
                </label>
                <select
                  value={settings.fonts.footer.size}
                  onChange={(e) => handleFontChange('footer', 'size', e.target.value)}
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {fontSizes.footer.map(size => (
                    <option key={size} value={size}>{size}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Font Weight
                </label>
                <select
                  value={settings.fonts.footer.weight}
                  onChange={(e) => handleFontChange('footer', 'weight', e.target.value)}
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {fontWeights.map(weight => (
                    <option key={weight.value} value={weight.value}>{weight.label}</option>
                  ))}
                </select>
              </div>
            </div>
            <div className="mt-4 p-4 bg-secondary-50 rounded-lg">
              <p 
                className="text-secondary-600"
                style={{
                  fontFamily: settings.fonts.footer.family,
                  fontSize: settings.fonts.footer.size,
                  fontWeight: settings.fonts.footer.weight
                }}
              >
                Sample footer text - © 2024 Hauling QR System. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-6 border-t border-secondary-200">
        <button
          onClick={handleReset}
          className="px-6 py-2 border border-secondary-300 rounded-md text-secondary-700 hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-secondary-500"
        >
          Reset to Defaults
        </button>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={handleSave}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Save Settings
          </button>
        </div>
      </div>

      {/* Help Text */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <span className="text-blue-500 text-lg">💡</span>
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-blue-800">Tips for customization:</h4>
            <ul className="mt-2 text-sm text-blue-700 space-y-1">
              <li>• Use the preview mode to see changes before saving</li>
              <li>• Logo images should be in PNG, JPG, or SVG format for best results</li>
              <li>• Keep logo dimensions reasonable (20-200px) for optimal display</li>
              <li>• Font changes will apply to headers, content, and footer sections respectively</li>
              <li>• Settings are saved locally in your browser</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppearanceSettings;