import React, { useState } from 'react';
import { getApiBaseUrl } from '../../../utils/network-utils';

/**
 * CleanupManagementPanel Component
 * 
 * Provides a user interface for analyzing and cleaning up unused JavaScript functions
 * in the codebase. Includes analysis, execution, and rollback capabilities.
 */
const CleanupManagementPanel = () => {
  // State for analysis results and options
  const [analysisResults, setAnalysisResults] = useState(null);
  const [cleanupResults, setCleanupResults] = useState(null);
  const [verificationResults, setVerificationResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('analysis');
  
  // Analysis options
  const [analysisOptions, setAnalysisOptions] = useState({
    includeServer: true,
    includeScripts: true,
    preserveCritical: true,
    maxFilesToAnalyze: 100
  });
  
  // Cleanup options
  const [cleanupOptions, setCleanupOptions] = useState({
    dryRun: true,
    createBackup: true,
    maxFilesToModify: 50
  });
  
  // Handle analysis options change
  const handleAnalysisOptionChange = (e) => {
    const { name, value, type, checked } = e.target;
    setAnalysisOptions({
      ...analysisOptions,
      [name]: type === 'checkbox' ? checked : (type === 'number' ? parseInt(value) : value)
    });
  };
  
  // Handle cleanup options change
  const handleCleanupOptionChange = (e) => {
    const { name, value, type, checked } = e.target;
    setCleanupOptions({
      ...cleanupOptions,
      [name]: type === 'checkbox' ? checked : (type === 'number' ? parseInt(value) : value)
    });
  };
  
  // Run code analysis
  const runAnalysis = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/system-health/cleanup/analyze`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(analysisOptions)
      });
      
      if (!response.ok) {
        throw new Error(`Failed to analyze code: ${response.statusText}`);
      }
      
      const result = await response.json();
      setAnalysisResults(result);
      setActiveTab('results');
      
    } catch (err) {
      console.error('Error analyzing code:', err);
      setError(`Failed to analyze code: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  // Execute cleanup
  const executeCleanup = async () => {
    if (!analysisResults) {
      setError('Analysis results are required for cleanup execution');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/system-health/cleanup/execute`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          analysisResults,
          ...cleanupOptions
        })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to execute cleanup: ${response.statusText}`);
      }
      
      const result = await response.json();
      setCleanupResults(result);
      setActiveTab('cleanup');
      
    } catch (err) {
      console.error('Error executing cleanup:', err);
      setError(`Failed to execute cleanup: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  // Verify system integrity
  const verifySystem = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/system-health/cleanup/verify`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to verify system: ${response.statusText}`);
      }
      
      const result = await response.json();
      setVerificationResults(result);
      
    } catch (err) {
      console.error('Error verifying system:', err);
      setError(`Failed to verify system: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  // Rollback changes
  const rollbackChanges = async () => {
    if (!cleanupResults?.backupPath) {
      setError('Backup path is required for rollback');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/system-health/cleanup/rollback`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          backupPath: cleanupResults.backupPath
        })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to rollback changes: ${response.statusText}`);
      }
      
      const result = await response.json();
      alert('Changes rolled back successfully');
      
      // Reset results
      setCleanupResults(null);
      setAnalysisResults(null);
      setActiveTab('analysis');
      
    } catch (err) {
      console.error('Error rolling back changes:', err);
      setError(`Failed to rollback changes: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="mt-6 bg-white rounded-lg shadow border border-secondary-200">
      <div className="px-6 py-4 border-b border-secondary-200">
        <h3 className="text-lg font-medium text-secondary-900">
          🧹 Code Cleanup Management
        </h3>
        <p className="text-sm text-secondary-500 mt-1">
          Analyze and clean up unused JavaScript functions to optimize codebase
        </p>
      </div>
      
      <div className="p-6">
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">❌ {error}</p>
          </div>
        )}
        
        {/* Tab Navigation */}
        <div className="border-b border-secondary-200 mb-4">
          <nav className="flex -mb-px">
            <button
              onClick={() => setActiveTab('analysis')}
              className={`py-2 px-4 text-sm font-medium ${
                activeTab === 'analysis'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-secondary-500 hover:text-secondary-700'
              }`}
            >
              Analysis
            </button>
            <button
              onClick={() => setActiveTab('results')}
              disabled={!analysisResults}
              className={`py-2 px-4 text-sm font-medium ${
                activeTab === 'results'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-secondary-500 hover:text-secondary-700'
              } ${!analysisResults ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              Results
            </button>
            <button
              onClick={() => setActiveTab('cleanup')}
              disabled={!cleanupResults}
              className={`py-2 px-4 text-sm font-medium ${
                activeTab === 'cleanup'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-secondary-500 hover:text-secondary-700'
              } ${!cleanupResults ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              Cleanup
            </button>
            <button
              onClick={() => setActiveTab('verification')}
              className={`py-2 px-4 text-sm font-medium ${
                activeTab === 'verification'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-secondary-500 hover:text-secondary-700'
              }`}
            >
              Verification
            </button>
          </nav>
        </div>
        
        {/* Analysis Tab */}
        {activeTab === 'analysis' && (
          <div>
            <div className="mb-4">
              <h4 className="text-md font-medium text-secondary-900 mb-2">
                Analysis Options
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="includeServer"
                      name="includeServer"
                      checked={analysisOptions.includeServer}
                      onChange={handleAnalysisOptionChange}
                      className="rounded border-secondary-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    />
                    <label htmlFor="includeServer" className="ml-2 text-sm text-secondary-700">
                      Include server files
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="includeScripts"
                      name="includeScripts"
                      checked={analysisOptions.includeScripts}
                      onChange={handleAnalysisOptionChange}
                      className="rounded border-secondary-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    />
                    <label htmlFor="includeScripts" className="ml-2 text-sm text-secondary-700">
                      Include script files
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="preserveCritical"
                      name="preserveCritical"
                      checked={analysisOptions.preserveCritical}
                      onChange={handleAnalysisOptionChange}
                      className="rounded border-secondary-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    />
                    <label htmlFor="preserveCritical" className="ml-2 text-sm text-secondary-700">
                      Preserve critical functions
                    </label>
                  </div>
                </div>
                <div>
                  <div className="mb-3">
                    <label htmlFor="maxFilesToAnalyze" className="block text-sm text-secondary-700 mb-1">
                      Max files to analyze
                    </label>
                    <input
                      type="number"
                      id="maxFilesToAnalyze"
                      name="maxFilesToAnalyze"
                      value={analysisOptions.maxFilesToAnalyze}
                      onChange={handleAnalysisOptionChange}
                      min="1"
                      max="500"
                      className="w-full rounded-md border-secondary-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    />
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-6">
              <button
                onClick={runAnalysis}
                disabled={loading}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {loading ? '⏳ Analyzing...' : '🔍 Analyze Code'}
              </button>
            </div>
          </div>
        )}
        
        {/* Results Tab */}
        {activeTab === 'results' && analysisResults && (
          <div>
            <div className="mb-4">
              <h4 className="text-md font-medium text-secondary-900 mb-2">
                Analysis Results
              </h4>
              <div className="bg-secondary-50 rounded-lg p-4 mb-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Total files scanned:</span> {analysisResults.stats.totalFiles}
                    </p>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Total functions:</span> {analysisResults.stats.totalFunctions}
                    </p>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Critical functions:</span> {analysisResults.stats.criticalFunctions}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Unused functions:</span> {analysisResults.stats.unusedFunctions}
                    </p>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Safe to remove:</span> {analysisResults.stats.safeToRemove}
                    </p>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Analysis time:</span> {new Date(analysisResults.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
              
              {analysisResults.unusedFunctions?.length > 0 ? (
                <div>
                  <h5 className="text-sm font-medium text-secondary-900 mb-2">
                    Unused Functions ({analysisResults.unusedFunctions.length})
                  </h5>
                  <div className="border border-secondary-200 rounded-lg overflow-hidden">
                    <table className="min-w-full divide-y divide-secondary-200">
                      <thead className="bg-secondary-50">
                        <tr>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            Function Name
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            File
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            Type
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            Exported
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-secondary-200">
                        {analysisResults.unusedFunctions.slice(0, 10).map((func, index) => (
                          <tr key={index}>
                            <td className="px-4 py-2 text-sm text-secondary-900">
                              {func.name}
                            </td>
                            <td className="px-4 py-2 text-sm text-secondary-500">
                              {func.file}
                            </td>
                            <td className="px-4 py-2 text-sm text-secondary-500">
                              {func.type}
                            </td>
                            <td className="px-4 py-2 text-sm text-secondary-500">
                              {func.exported ? 'Yes' : 'No'}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    {analysisResults.unusedFunctions.length > 10 && (
                      <div className="px-4 py-2 bg-secondary-50 text-sm text-secondary-500">
                        Showing 10 of {analysisResults.unusedFunctions.length} unused functions
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-sm text-green-700">
                    ✅ No unused functions detected. Codebase appears clean.
                  </p>
                </div>
              )}
            </div>
            
            {analysisResults.unusedFunctions?.length > 0 && (
              <div className="mt-6">
                <h4 className="text-md font-medium text-secondary-900 mb-2">
                  Cleanup Options
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="dryRun"
                        name="dryRun"
                        checked={cleanupOptions.dryRun}
                        onChange={handleCleanupOptionChange}
                        className="rounded border-secondary-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                      <label htmlFor="dryRun" className="ml-2 text-sm text-secondary-700">
                        Dry run (no actual changes)
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="createBackup"
                        name="createBackup"
                        checked={cleanupOptions.createBackup}
                        onChange={handleCleanupOptionChange}
                        className="rounded border-secondary-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                      <label htmlFor="createBackup" className="ml-2 text-sm text-secondary-700">
                        Create backup before cleanup
                      </label>
                    </div>
                  </div>
                  <div>
                    <div className="mb-3">
                      <label htmlFor="maxFilesToModify" className="block text-sm text-secondary-700 mb-1">
                        Max files to modify
                      </label>
                      <input
                        type="number"
                        id="maxFilesToModify"
                        name="maxFilesToModify"
                        value={cleanupOptions.maxFilesToModify}
                        onChange={handleCleanupOptionChange}
                        min="1"
                        max="100"
                        className="w-full rounded-md border-secondary-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="mt-4">
                  <button
                    onClick={executeCleanup}
                    disabled={loading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    {loading ? '⏳ Executing...' : '🧹 Execute Cleanup'}
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* Cleanup Tab */}
        {activeTab === 'cleanup' && cleanupResults && (
          <div>
            <div className="mb-4">
              <h4 className="text-md font-medium text-secondary-900 mb-2">
                Cleanup Results
              </h4>
              <div className="bg-secondary-50 rounded-lg p-4 mb-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Files modified:</span> {cleanupResults.stats.filesModified}
                    </p>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Functions removed:</span> {cleanupResults.stats.functionsRemoved}
                    </p>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Errors:</span> {cleanupResults.stats.errors}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Dry run:</span> {cleanupResults.dryRun ? 'Yes' : 'No'}
                    </p>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Backup created:</span> {cleanupResults.backupPath ? 'Yes' : 'No'}
                    </p>
                    <p className="text-sm text-secondary-700">
                      <span className="font-medium">Execution time:</span> {new Date(cleanupResults.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
              
              {cleanupResults.modifiedFiles?.length > 0 && (
                <div className="mb-4">
                  <h5 className="text-sm font-medium text-secondary-900 mb-2">
                    Modified Files ({cleanupResults.modifiedFiles.length})
                  </h5>
                  <div className="border border-secondary-200 rounded-lg p-4 max-h-40 overflow-y-auto">
                    <ul className="space-y-1">
                      {cleanupResults.modifiedFiles.map((file, index) => (
                        <li key={index} className="text-sm text-secondary-700">
                          {file}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}
              
              {!cleanupResults.dryRun && cleanupResults.backupPath && (
                <div className="mt-6">
                  <button
                    onClick={rollbackChanges}
                    disabled={loading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    {loading ? '⏳ Rolling back...' : '↩️ Rollback Changes'}
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
        
        {/* Verification Tab */}
        {activeTab === 'verification' && (
          <div>
            <div className="mb-4">
              <h4 className="text-md font-medium text-secondary-900 mb-2">
                System Verification
              </h4>
              <p className="text-sm text-secondary-600 mb-4">
                Verify system integrity after cleanup operations
              </p>
              
              <button
                onClick={verifySystem}
                disabled={loading}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {loading ? '⏳ Verifying...' : '🔍 Verify System Integrity'}
              </button>
              
              {verificationResults && (
                <div className="mt-4">
                  <div className={`p-4 rounded-md ${
                    verificationResults.success
                      ? 'bg-green-50 border border-green-200'
                      : 'bg-red-50 border border-red-200'
                  }`}>
                    <p className={`text-sm font-medium ${
                      verificationResults.success ? 'text-green-700' : 'text-red-700'
                    }`}>
                      {verificationResults.success
                        ? '✅ System integrity check passed'
                        : '❌ System integrity check failed'}
                    </p>
                    
                    {verificationResults.missingFiles?.length > 0 && (
                      <div className="mt-2">
                        <p className="text-sm text-red-700 font-medium">Missing critical files:</p>
                        <ul className="mt-1 space-y-1">
                          {verificationResults.missingFiles.map((file, index) => (
                            <li key={index} className="text-sm text-red-600">
                              • {file}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    
                    {verificationResults.serverRequireError && (
                      <p className="mt-2 text-sm text-red-600">
                        Server require error: {verificationResults.serverRequireError}
                      </p>
                    )}
                    
                    {verificationResults.dbConnectError && (
                      <p className="mt-2 text-sm text-red-600">
                        Database connection error: {verificationResults.dbConnectError}
                      </p>
                    )}
                    
                    <p className="mt-2 text-xs text-secondary-500">
                      Verification time: {new Date(verificationResults.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        
        {/* Help Text */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h5 className="text-sm font-medium text-blue-900 mb-2">ℹ️ About Code Cleanup:</h5>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Analyzes server and script files for unused JavaScript functions</li>
            <li>• Preserves critical functions (routes, middleware, database operations)</li>
            <li>• Creates backups before making any changes</li>
            <li>• Provides rollback capability if issues are detected</li>
            <li>• Verifies system integrity after cleanup operations</li>
            <li>• Use dry run mode to preview changes without modifying files</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default CleanupManagementPanel;