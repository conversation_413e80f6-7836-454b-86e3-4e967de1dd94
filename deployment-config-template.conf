# ============================================================================
# HAULING QR TRIP SYSTEM - DEPLOYMENT CONFIGURATION TEMPLATE
# ============================================================================
# Copy this file and customize for your deployment
# Usage: ./deploy-hauling-qr-ubuntu.sh --config-file your-config.conf

# ============================================================================
# BASIC CONFIGURATION
# ============================================================================

# Domain Configuration
DOMAIN_NAME="hauling.example.com"
CLOUDFLARE_SSL_MODE="full"  # Options: flexible, full, full_strict

# Database Configuration
DB_PASSWORD="your_secure_database_password_here"
# Leave empty for auto-generation: DB_PASSWORD=""

# Security Configuration
JWT_SECRET="your_jwt_secret_64_characters_minimum_here"
# Leave empty for auto-generation: JWT_SECRET=""

# Admin User Configuration
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="your_admin_password_here"

# Repository Configuration
GIT_REPO_URL="https://github.com/your-username/hauling-qr-system.git"
# Leave empty for manual upload: GIT_REPO_URL=""

# ============================================================================
# OPTIONAL FEATURES
# ============================================================================

# System Monitoring
ENABLE_MONITORING="true"  # Options: true, false

# Automatic Backups
ENABLE_BACKUPS="true"     # Options: true, false
BACKUP_SCHEDULE="0 2 * * *"  # Cron format: daily at 2 AM

# Environment
ENVIRONMENT="production"  # Options: production, staging, development

# ============================================================================
# ADVANCED CONFIGURATION (Optional)
# ============================================================================

# Server Configuration
NODE_VERSION="18"
POSTGRES_VERSION="15"
APP_USER="hauling"
APP_DIR="/opt/hauling-qr-system"

# Performance Settings
PM2_INSTANCES="max"       # Options: max, number (e.g., 4)
MAX_MEMORY_RESTART="2G"   # PM2 memory restart threshold
NODE_MAX_OLD_SPACE="2048" # Node.js heap size in MB

# Security Settings
FAIL2BAN_BANTIME="3600"   # Ban time in seconds
FAIL2BAN_MAXRETRY="5"     # Max retry attempts
UFW_ENABLE="true"         # Enable firewall

# SSL Configuration (for Full SSL mode)
SSL_CERT_PATH="/etc/nginx/ssl/certificate.crt"
SSL_KEY_PATH="/etc/nginx/ssl/private.key"
SSL_COUNTRY="US"
SSL_STATE="State"
SSL_CITY="City"
SSL_ORG="Organization"

# Rate Limiting
NGINX_RATE_LIMIT_API="20r/s"     # API rate limit
NGINX_RATE_LIMIT_AUTH="5r/m"     # Auth rate limit
NGINX_RATE_LIMIT_GENERAL="10r/s" # General rate limit

# Database Optimization
DB_SHARED_BUFFERS="256MB"        # PostgreSQL shared buffers
DB_EFFECTIVE_CACHE_SIZE="1GB"    # PostgreSQL effective cache size
DB_WORK_MEM="4MB"                # PostgreSQL work memory
DB_MAX_CONNECTIONS="100"         # PostgreSQL max connections

# Monitoring Configuration
HEALTH_CHECK_INTERVAL="*/5 * * * *"  # Cron format: every 5 minutes
PERFORMANCE_CHECK_INTERVAL="*/10 * * * *"  # Cron format: every 10 minutes
REPORT_GENERATION_TIME="0 6 * * *"   # Cron format: daily at 6 AM

# Backup Configuration
BACKUP_RETENTION_DAYS="7"        # Keep backups for 7 days
FULL_BACKUP_SCHEDULE="0 3 * * 0" # Weekly full backup on Sunday at 3 AM
BACKUP_COMPRESSION="true"        # Compress backups

# Logging Configuration
LOG_LEVEL="info"                 # Options: error, warn, info, debug
LOG_ROTATION_SIZE="100M"         # Rotate logs when they reach this size
LOG_RETENTION_DAYS="52"          # Keep logs for 52 days

# Email Configuration (for alerts)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your_smtp_password"
ALERT_EMAIL="<EMAIL>"

# ============================================================================
# CLOUDFLARE SPECIFIC SETTINGS
# ============================================================================

# Cloudflare API (optional, for advanced SSL management)
CLOUDFLARE_API_TOKEN=""          # Leave empty if not using API
CLOUDFLARE_ZONE_ID=""            # Leave empty if not using API

# Cloudflare Optimization Settings
CF_MINIFY_HTML="true"
CF_MINIFY_CSS="true"
CF_MINIFY_JS="true"
CF_BROTLI="true"
CF_CACHE_LEVEL="standard"
CF_BROWSER_CACHE_TTL="14400"     # 4 hours

# ============================================================================
# DEVELOPMENT/STAGING OVERRIDES
# ============================================================================

# Uncomment and modify for staging environment
# ENVIRONMENT="staging"
# DOMAIN_NAME="staging.hauling.example.com"
# DB_PASSWORD="staging_password"
# ENABLE_MONITORING="false"
# ENABLE_BACKUPS="false"

# Uncomment and modify for development environment
# ENVIRONMENT="development"
# DOMAIN_NAME="dev.hauling.example.com"
# DB_PASSWORD="dev_password"
# ENABLE_MONITORING="false"
# ENABLE_BACKUPS="false"
# LOG_LEVEL="debug"

# ============================================================================
# NOTES
# ============================================================================

# 1. Replace all example values with your actual configuration
# 2. Ensure passwords are strong and unique
# 3. Keep this file secure and don't commit it to version control
# 4. Test configuration in staging before production deployment
# 5. Backup this configuration file after deployment

# ============================================================================
# VALIDATION RULES
# ============================================================================

# Domain: Must be valid FQDN format
# Passwords: Minimum 12 characters for DB, 8 for admin
# Email: Must be valid email format
# SSL Mode: Must be one of: flexible, full, full_strict
# Environment: Must be one of: production, staging, development
# Boolean values: Must be "true" or "false"
# Cron expressions: Must be valid cron format