/**
 * Automated Fix Service
 * 
 * Provides automated fixes for system health issues across three core modules:
 * - Shift Management
 * - Assignment Management
 * - Trip Monitoring
 * 
 * Integrates with existing database functions and monitoring scripts
 * to provide a unified interface for automated fixes.
 */

const { getClient } = require('../config/database');

class AutomatedFixService {
  constructor() {
    this.logger = console;
  }

  /**
   * Fix shift management issues
   * @returns {Promise<Object>} Result of the fix operation
   */
  async fixShiftManagement() {
    try {
      const client = await getClient();
      
      try {
        // Begin transaction
        await client.query('BEGIN');
        
        // Execute the schedule_auto_activation function
        await client.query('SELECT schedule_auto_activation()');
        
        // Get updated statistics
        const stats = await client.query(`
          SELECT
            COUNT(*) FILTER (WHERE status = 'active') as active_count,
            COUNT(*) FILTER (WHERE status = 'scheduled') as scheduled_count,
            COUNT(*) FILTER (WHERE status = 'completed') as completed_count,
            COUNT(*) as total_count
          FROM driver_shifts
        `);
        
        // Log the fix operation
        await client.query(`
          INSERT INTO system_logs (
            log_type,
            message,
            details
          ) VALUES (
            'SHIFT_AUTO_FIX',
            'Automated fix for shift management issues',
            $1
          )
        `, [JSON.stringify({
          timestamp: new Date().toISOString(),
          statistics: stats.rows[0]
        })]);
        
        // Commit transaction
        await client.query('COMMIT');
        
        return {
          success: true,
          message: 'Shift management issues fixed successfully',
          details: 'Executed schedule_auto_activation() to correct shift statuses',
          affectedRecords: {
            active: stats.rows[0].active_count,
            scheduled: stats.rows[0].scheduled_count,
            completed: stats.rows[0].completed_count,
            total: stats.rows[0].total_count
          },
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        // Rollback transaction on error
        await client.query('ROLLBACK');
        throw error;
      } finally {
        // Release client back to pool
        client.release();
      }
    } catch (error) {
      this.logger.error('Error fixing shift management issues:', error);
      return {
        success: false,
        message: 'Failed to fix shift management issues',
        details: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Fix assignment management issues
   * @returns {Promise<Object>} Result of the fix operation
   */
  async fixAssignmentManagement() {
    try {
      const client = await getClient();
      
      try {
        // Begin transaction
        await client.query('BEGIN');
        
        // Synchronize assignments with active shifts
        const result = await client.query(`
          WITH active_shifts AS (
            SELECT 
              ds.truck_id,
              ds.driver_id,
              ds.shift_type
            FROM 
              driver_shifts ds
            WHERE 
              ds.status = 'active'
              AND ds.start_date <= CURRENT_DATE
              AND ds.end_date >= CURRENT_DATE
          ),
          updated_assignments AS (
            UPDATE assignments a
            SET 
              driver_id = as.driver_id,
              updated_at = CURRENT_TIMESTAMP
            FROM 
              active_shifts as
            WHERE 
              a.truck_id = as.truck_id
              AND (a.driver_id IS NULL OR a.driver_id != as.driver_id)
            RETURNING a.id
          )
          SELECT COUNT(*) as updated_count FROM updated_assignments
        `);
        
        // Log the fix operation
        await client.query(`
          INSERT INTO system_logs (
            log_type,
            message,
            details
          ) VALUES (
            'ASSIGNMENT_AUTO_FIX',
            'Automated fix for assignment management issues',
            $1
          )
        `, [JSON.stringify({
          timestamp: new Date().toISOString(),
          updated_assignments: result.rows[0].updated_count
        })]);
        
        // Commit transaction
        await client.query('COMMIT');
        
        return {
          success: true,
          message: 'Assignment management issues fixed successfully',
          details: 'Synchronized assignments with active shifts',
          affectedRecords: {
            updated_assignments: result.rows[0].updated_count
          },
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        // Rollback transaction on error
        await client.query('ROLLBACK');
        throw error;
      } finally {
        // Release client back to pool
        client.release();
      }
    } catch (error) {
      this.logger.error('Error fixing assignment management issues:', error);
      return {
        success: false,
        message: 'Failed to fix assignment management issues',
        details: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Fix trip monitoring issues
   * @returns {Promise<Object>} Result of the fix operation
   */
  async fixTripMonitoring() {
    try {
      const client = await getClient();
      
      try {
        // Begin transaction
        await client.query('BEGIN');
        
        // Fix invalid trip statuses (using trip_logs table)
        const invalidStatusResult = await client.query(`
          WITH invalid_trips AS (
            SELECT id, status
            FROM trip_logs
            WHERE status NOT IN ('assigned', 'loading_start', 'loading_end', 'unloading_start', 'unloading_end', 'trip_completed', 'cancelled', 'stopped')
          ),
          fixed_trips AS (
            UPDATE trip_logs
            SET
              status = 'assigned',
              updated_at = CURRENT_TIMESTAMP
            FROM
              invalid_trips
            WHERE
              trip_logs.id = invalid_trips.id
            RETURNING trip_logs.id
          )
          SELECT COUNT(*) as fixed_count FROM fixed_trips
        `);
        
        // Log the fix operation
        await client.query(`
          INSERT INTO system_logs (
            log_type,
            message,
            details
          ) VALUES (
            'TRIP_AUTO_FIX',
            'Automated fix for trip monitoring issues',
            $1
          )
        `, [JSON.stringify({
          timestamp: new Date().toISOString(),
          fixed_invalid_statuses: invalidStatusResult.rows[0].fixed_count
        })]);
        
        // Commit transaction
        await client.query('COMMIT');
        
        return {
          success: true,
          message: 'Trip monitoring issues fixed successfully',
          details: 'Fixed invalid trip statuses',
          affectedRecords: {
            fixed_invalid_statuses: invalidStatusResult.rows[0].fixed_count
          },
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        // Rollback transaction on error
        await client.query('ROLLBACK');
        throw error;
      } finally {
        // Release client back to pool
        client.release();
      }
    } catch (error) {
      this.logger.error('Error fixing trip monitoring issues:', error);
      return {
        success: false,
        message: 'Failed to fix trip monitoring issues',
        details: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

module.exports = AutomatedFixService;