-- Migration: Fix shift status logic - FINAL CORRECT VERSION
-- Purpose: Fix the incorrect status calculation logic
-- Created: 2025-07-16

-- Drop all existing problematic functions
DROP FUNCTION IF EXISTS evaluate_shift_status CASCADE;
DROP FUNCTION IF EXISTS schedule_auto_activation CASCADE;
DROP FUNCTION IF EXISTS update_all_shift_statuses CASCADE;

-- Create the CORRECT evaluate_shift_status function
CREATE FUNCTION evaluate_shift_status(
    p_shift_id INTEGER,
    p_reference_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
) RETURNS TEXT AS $$
DECLARE
    v_shift RECORD;
    v_current_date DATE;
    v_current_time TIME;
    v_is_overnight BOOLEAN;
    v_is_within_date_range BOOLEAN;
    v_is_within_time_window BOOLEAN;
    v_is_past_completion BOOLEAN;
BEGIN
    -- Get shift details
    SELECT start_date, end_date, start_time, end_time, status
    INTO v_shift
    FROM driver_shifts
    WHERE id = p_shift_id;

    IF NOT FOUND THEN
        RETURN 'error';
    END IF;

    -- Never override completed or cancelled status (immutable)
    IF v_shift.status IN ('completed', 'cancelled') THEN
        RETURN v_shift.status;
    END IF;

    -- Extract current date and time
    v_current_date := p_reference_timestamp::DATE;
    v_current_time := p_reference_timestamp::TIME;

    -- Check if overnight shift
    v_is_overnight := v_shift.end_time < v_shift.start_time;

    -- CRITICAL FIX: Date range check - current date must be within shift date range
    v_is_within_date_range := v_current_date BETWEEN v_shift.start_date AND v_shift.end_date;

    -- Time window check
    IF v_is_overnight THEN
        -- Night shift: active if time >= start_time OR time <= end_time
        v_is_within_time_window := (v_current_time >= v_shift.start_time OR v_current_time <= v_shift.end_time);
    ELSE
        -- Day shift: active if time between start_time and end_time
        v_is_within_time_window := (v_current_time BETWEEN v_shift.start_time AND v_shift.end_time);
    END IF;

    -- CRITICAL FIX: Completion logic - only completed if PAST the end_date
    -- NOT if past end_time on current day within the date range
    IF v_current_date > v_shift.end_date THEN
        -- Past the end date - definitely completed
        v_is_past_completion := TRUE;
    ELSIF v_current_date < v_shift.start_date THEN
        -- Before start date - definitely not completed
        v_is_past_completion := FALSE;
    ELSE
        -- Within date range - never completed, only active or scheduled based on time
        v_is_past_completion := FALSE;
    END IF;

    -- Apply status rules in priority order
    IF v_is_past_completion THEN
        RETURN 'completed';
    ELSIF v_is_within_date_range AND v_is_within_time_window THEN
        RETURN 'active';
    ELSE
        RETURN 'scheduled';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create the corrected auto-activation function
CREATE FUNCTION schedule_auto_activation() RETURNS void AS $$
DECLARE
    rec RECORD;
    new_status TEXT;
    count_activated INTEGER := 0;
    count_completed INTEGER := 0;
BEGIN
    FOR rec IN SELECT id, status::TEXT as status FROM driver_shifts WHERE status != 'cancelled'
    LOOP
        new_status := evaluate_shift_status(rec.id, CURRENT_TIMESTAMP);
        IF new_status != rec.status AND new_status != 'error' THEN
            UPDATE driver_shifts 
            SET status = new_status::shift_status, updated_at = CURRENT_TIMESTAMP 
            WHERE id = rec.id;
            
            IF rec.status = 'scheduled' AND new_status = 'active' THEN 
                count_activated := count_activated + 1; 
            END IF;
            IF rec.status = 'active' AND new_status = 'completed' THEN 
                count_completed := count_completed + 1; 
            END IF;
        END IF;
    END LOOP;
    RAISE NOTICE 'Auto-activation: % activated, % completed', count_activated, count_completed;
END;
$$ LANGUAGE plpgsql;

-- Create the update_all_shift_statuses function (without the problematic 3rd parameter)
CREATE FUNCTION update_all_shift_statuses(
    p_reference_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
) RETURNS TABLE (
    updated_count INTEGER,
    activated_count INTEGER,
    completed_count INTEGER,
    scheduled_count INTEGER
) AS $$
DECLARE
    v_updated_count INTEGER := 0;
    v_activated_count INTEGER := 0;
    v_completed_count INTEGER := 0;
    v_scheduled_count INTEGER := 0;
    v_shift_record RECORD;
    v_new_status TEXT;
    v_old_status TEXT;
BEGIN
    FOR v_shift_record IN
        SELECT id, status::TEXT as status, truck_id, driver_id, shift_type
        FROM driver_shifts
        WHERE status != 'cancelled'
        ORDER BY truck_id, start_time
    LOOP
        v_old_status := v_shift_record.status;
        v_new_status := evaluate_shift_status(v_shift_record.id, p_reference_timestamp);

        IF v_new_status != v_old_status AND v_new_status != 'error' THEN
            UPDATE driver_shifts
            SET status = v_new_status::shift_status,
                updated_at = p_reference_timestamp
            WHERE id = v_shift_record.id;

            v_updated_count := v_updated_count + 1;

            CASE v_new_status
                WHEN 'active' THEN v_activated_count := v_activated_count + 1;
                WHEN 'completed' THEN v_completed_count := v_completed_count + 1;
                WHEN 'scheduled' THEN v_scheduled_count := v_scheduled_count + 1;
            END CASE;
        END IF;
    END LOOP;

    RETURN QUERY SELECT v_updated_count, v_activated_count, v_completed_count, v_scheduled_count;
END;
$$ LANGUAGE plpgsql;

-- Add proper comments
COMMENT ON FUNCTION evaluate_shift_status(INTEGER, TIMESTAMP WITH TIME ZONE) IS 
'Correctly evaluates shift status: completed only when past end_date, not end_time within date range';

COMMENT ON FUNCTION schedule_auto_activation() IS 
'Auto-activates and completes shifts based on corrected date/time logic';

COMMENT ON FUNCTION update_all_shift_statuses(TIMESTAMP WITH TIME ZONE) IS 
'Updates all shift statuses with corrected logic - no boolean parameter';