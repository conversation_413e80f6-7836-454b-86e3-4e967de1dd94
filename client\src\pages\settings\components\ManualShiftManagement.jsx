import React, { useState, useEffect } from 'react';
import axios from 'axios';

const ManualShiftManagement = () => {
  const [activeTab, setActiveTab] = useState('active');
  const [activeShifts, setActiveShifts] = useState([]);
  const [scheduledShifts, setScheduledShifts] = useState([]);
  const [summary, setSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [completeDialogOpen, setCompleteDialogOpen] = useState(false);
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [selectedShift, setSelectedShift] = useState(null);
  const [notes, setNotes] = useState('');
  const [reason, setReason] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      // Fetch active shifts
      try {
        const activeResponse = await axios.get('/api/manual-shift-management/active');
        setActiveShifts(activeResponse.data.data || []);
      } catch (err) {
        console.error('Error fetching active shifts:', err);
        setActiveShifts([]);
      }

      // Fetch scheduled shifts
      try {
        const scheduledResponse = await axios.get('/api/manual-shift-management/scheduled');
        setScheduledShifts(scheduledResponse.data.data || []);
      } catch (err) {
        console.error('Error fetching scheduled shifts:', err);
        setScheduledShifts([]);
      }

      // Fetch summary
      try {
        const summaryResponse = await axios.get('/api/manual-shift-management/summary');
        setSummary(summaryResponse.data.data || {
          active_count: 0,
          scheduled_count: 0,
          completed_count: 0,
          cancelled_count: 0,
          total_count: 0
        });
      } catch (err) {
        console.error('Error fetching summary:', err);
        setSummary({
          active_count: 0,
          scheduled_count: 0,
          completed_count: 0,
          cancelled_count: 0,
          total_count: 0
        });
      }
    } catch (err) {
      setError('Failed to fetch shift data. Please try again later.');
      console.error('Error in fetchData:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  const handleRefreshStatuses = async () => {
    setRefreshing(true);
    setError(null);
    try {
      const response = await axios.post('/api/manual-shift-management/refresh', {});
      setSuccessMessage('Shift statuses refreshed successfully');
      setTimeout(() => setSuccessMessage(''), 5000);
      fetchData();
    } catch (err) {
      console.error('Error refreshing shift statuses:', err);
      
      // Provide more specific error messages based on the error
      if (err.response?.status === 404) {
        setError('The refresh endpoint was not found. This may be because the server is not properly configured.');
      } else if (err.response?.status === 500) {
        setError('Server error while refreshing shift statuses. This may be because the database is not properly set up or the schedule_auto_activation function does not exist.');
      } else {
        setError(err.response?.data?.message || 'Failed to refresh shift statuses. Please try again later.');
      }
    } finally {
      setRefreshing(false);
    }
  };

  const openCompleteDialog = (shift) => {
    setSelectedShift(shift);
    setNotes('');
    setCompleteDialogOpen(true);
  };

  const openCancelDialog = (shift) => {
    setSelectedShift(shift);
    setReason('');
    setCancelDialogOpen(true);
  };

  const handleCompleteShift = async () => {
    if (!selectedShift) return;
    
    setCompleteDialogOpen(false);
    setLoading(true);
    setError(null);
    
    try {
      await axios.post(`/api/manual-shift-management/complete/${selectedShift.id}`, { notes });
      setSuccessMessage(`Shift for ${selectedShift.driver_name || 'Unknown'} completed successfully`);
      setTimeout(() => setSuccessMessage(''), 5000);
      fetchData();
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to complete shift');
      console.error('Error completing shift:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelShift = async () => {
    if (!selectedShift) return;
    
    setCancelDialogOpen(false);
    setLoading(true);
    setError(null);
    
    try {
      await axios.post(`/api/manual-shift-management/cancel/${selectedShift.id}`, { reason });
      setSuccessMessage(`Shift for ${selectedShift.driver_name || 'Unknown'} cancelled successfully`);
      setTimeout(() => setSuccessMessage(''), 5000);
      fetchData();
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to cancel shift');
      console.error('Error cancelling shift:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatDateTime = (date, time) => {
    try {
      const dateObj = new Date(date);
      const [hours, minutes] = time.split(':');
      dateObj.setHours(parseInt(hours, 10), parseInt(minutes, 10));
      return dateObj.toLocaleString();
    } catch (err) {
      return `${date} ${time}`;
    }
  };

  const getShiftTypeLabel = (shiftType) => {
    return shiftType === 'day' ? 'Day Shift' : 'Night Shift';
  };

  const renderShiftTable = (shifts) => {
    if (!Array.isArray(shifts)) {
      return (
        <div className="mt-4 p-4 bg-yellow-100 border border-yellow-400 rounded">
          <p className="text-yellow-700">Error loading shift data. Please try refreshing the page.</p>
        </div>
      );
    }
    
    return (
      <div className="mt-4 overflow-x-auto">
        <table className="min-w-full bg-white border border-gray-200">
          <thead className="bg-gray-100">
            <tr>
              <th className="py-2 px-4 border-b">Driver</th>
              <th className="py-2 px-4 border-b">Truck</th>
              <th className="py-2 px-4 border-b">Shift Type</th>
              <th className="py-2 px-4 border-b">Start</th>
              <th className="py-2 px-4 border-b">End</th>
              <th className="py-2 px-4 border-b">Actions</th>
            </tr>
          </thead>
          <tbody>
            {shifts.length === 0 ? (
              <tr>
                <td colSpan={6} className="py-4 px-4 text-center">No shifts found</td>
              </tr>
            ) : (
              shifts.map((shift) => (
                <tr key={shift.id} className="hover:bg-gray-50">
                  <td className="py-2 px-4 border-b">{shift.driver_name || 'Unknown'}</td>
                  <td className="py-2 px-4 border-b">{shift.truck_number || 'Unknown'}</td>
                  <td className="py-2 px-4 border-b">
                    <span className={`px-2 py-1 rounded text-xs font-semibold ${shift.shift_type === 'day' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'}`}>
                      {getShiftTypeLabel(shift.shift_type)}
                    </span>
                  </td>
                  <td className="py-2 px-4 border-b">{formatDateTime(shift.start_date, shift.start_time)}</td>
                  <td className="py-2 px-4 border-b">{formatDateTime(shift.end_date, shift.end_time)}</td>
                  <td className="py-2 px-4 border-b">
                    {activeTab === 'active' && (
                      <button 
                        className="bg-green-500 hover:bg-green-600 text-white py-1 px-3 rounded text-sm mr-2"
                        onClick={() => openCompleteDialog(shift)}
                      >
                        Complete
                      </button>
                    )}
                    <button 
                      className="bg-red-500 hover:bg-red-600 text-white py-1 px-3 rounded text-sm"
                      onClick={() => openCancelDialog(shift)}
                    >
                      Cancel
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    );
  };

  const renderSummary = () => {
    if (!summary) return null;
    
    return (
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mt-4">
        <div className="bg-white p-4 rounded shadow">
          <div className="text-blue-600 font-semibold">Active</div>
          <div className="text-2xl font-bold">{summary.active_count}</div>
        </div>
        <div className="bg-white p-4 rounded shadow">
          <div className="text-indigo-600 font-semibold">Scheduled</div>
          <div className="text-2xl font-bold">{summary.scheduled_count}</div>
        </div>
        <div className="bg-white p-4 rounded shadow">
          <div className="text-green-600 font-semibold">Completed</div>
          <div className="text-2xl font-bold">{summary.completed_count}</div>
        </div>
        <div className="bg-white p-4 rounded shadow">
          <div className="text-red-600 font-semibold">Cancelled</div>
          <div className="text-2xl font-bold">{summary.cancelled_count}</div>
        </div>
        <div className="bg-white p-4 rounded shadow">
          <div className="text-gray-600 font-semibold">Total</div>
          <div className="text-2xl font-bold">{summary.total_count}</div>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">Manual Shift Management</h2>
        <button 
          className={`bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded ${refreshing ? 'opacity-50 cursor-not-allowed' : ''}`}
          onClick={handleRefreshStatuses}
          disabled={refreshing}
        >
          {refreshing ? 'Refreshing...' : 'Refresh Statuses'}
        </button>
      </div>

      {successMessage && (
        <div className="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4">
          {successMessage}
        </div>
      )}

      {error && (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4">
          {error}
        </div>
      )}

      {renderSummary()}

      <div className="mt-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex">
            <button
              className={`py-2 px-4 border-b-2 font-medium text-sm ${activeTab === 'active' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
              onClick={() => handleTabChange('active')}
            >
              Active Shifts
            </button>
            <button
              className={`ml-8 py-2 px-4 border-b-2 font-medium text-sm ${activeTab === 'scheduled' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
              onClick={() => handleTabChange('scheduled')}
            >
              Scheduled Shifts
            </button>
          </nav>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <div className="mt-4">
            {activeTab === 'active' ? renderShiftTable(activeShifts) : renderShiftTable(scheduledShifts)}
          </div>
        )}
      </div>

      {/* Complete Shift Dialog */}
      {completeDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium mb-4">Complete Shift</h3>
            <p className="mb-4">
              Are you sure you want to manually complete the shift for {selectedShift?.driver_name || 'this driver'}?
              This action cannot be undone.
            </p>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Completion Notes (Optional)
              </label>
              <textarea
                className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows="3"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
              ></textarea>
            </div>
            <div className="flex justify-end space-x-2">
              <button
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded"
                onClick={() => setCompleteDialogOpen(false)}
              >
                Cancel
              </button>
              <button
                className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
                onClick={handleCompleteShift}
              >
                Complete Shift
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Cancel Shift Dialog */}
      {cancelDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium mb-4">Cancel Shift</h3>
            <p className="mb-4">
              Are you sure you want to cancel the shift for {selectedShift?.driver_name || 'this driver'}?
              This action cannot be undone.
            </p>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Cancellation Reason (Required)
              </label>
              <textarea
                className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows="3"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                required
              ></textarea>
            </div>
            <div className="flex justify-end space-x-2">
              <button
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded"
                onClick={() => setCancelDialogOpen(false)}
              >
                Back
              </button>
              <button
                className={`bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded ${!reason.trim() ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={handleCancelShift}
                disabled={!reason.trim()}
              >
                Cancel Shift
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ManualShiftManagement;