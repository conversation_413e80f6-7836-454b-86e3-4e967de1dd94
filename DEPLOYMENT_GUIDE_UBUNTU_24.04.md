# Hauling QR Trip System - Complete Deployment Guide for Ubuntu 24.04

## 🚀 Quick Start

This guide provides comprehensive instructions for deploying the Hauling QR Trip Management System on Ubuntu 24.04 VPS with Cloudflare SSL integration.

### Prerequisites

- **Server**: Ubuntu 24.04 LTS VPS with at least 2GB RAM and 20GB storage
- **Domain**: Domain name pointed to your server IP
- **Cloudflare**: Account with domain configured
- **Access**: Root or sudo privileges on the server
- **Network**: Server accessible via SSH (port 22)

## 📋 Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Cloudflare    │────│   Nginx Proxy    │────│   Node.js App   │
│   (SSL/CDN)     │    │   (Port 80/443)  │    │   (Port 5000)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                       ┌──────────────────┐
                       │   PostgreSQL     │
                       │   (Port 5432)    │
                       └──────────────────┘
```

**Components:**
- **Frontend**: React.js (served by <PERSON>inx)
- **Backend**: Node.js/Express API (managed by PM2)
- **Database**: PostgreSQL 15
- **Reverse Proxy**: Nginx with Cloudflare integration
- **Process Manager**: PM2 with clustering
- **SSL**: Cloudflare (Flexible/Full/Full Strict modes)

## 🎯 Deployment Methods

### Method 1: Automated Deployment (Recommended)

Use the interactive auto-deployment script for the easiest setup:

```bash
# Download the deployment script
wget https://raw.githubusercontent.com/your-repo/hauling-qr-system/main/deploy-hauling-qr-ubuntu.sh

# Make it executable
chmod +x deploy-hauling-qr-ubuntu.sh

# Run the deployment
./deploy-hauling-qr-ubuntu.sh
```

The script will guide you through:
1. **Domain Configuration**: Enter your domain name
2. **SSL Mode Selection**: Choose Cloudflare SSL mode
3. **Database Setup**: Auto-generate or set custom passwords
4. **Admin Account**: Create system administrator
5. **Optional Features**: Enable monitoring and backups
6. **Environment**: Production, staging, or development

### Method 2: Manual Deployment

Follow the step-by-step manual installation process below.## 🔧 Manual Deployment Steps

### Step 1: Initial Server Setup

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget gnupg2 software-properties-common \
    apt-transport-https ca-certificates git build-essential \
    unzip ufw fail2ban htop tree jq openssl nginx \
    postgresql postgresql-contrib redis-server supervisor

# Create application user
sudo useradd -r -s /bin/bash -d /opt/hauling-qr-system -m hauling
sudo usermod -aG sudo hauling
```

### Step 2: Install Node.js 18.x

```bash
# Add NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -

# Install Node.js and PM2
sudo apt install -y nodejs
sudo npm install -g pm2

# Verify installation
node --version  # Should show v18.x.x
npm --version   # Should show 9.x.x or higher
```

### Step 3: Configure PostgreSQL

```bash
# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql << 'EOF'
CREATE DATABASE hauling_qr_system;
CREATE USER hauling_user WITH ENCRYPTED PASSWORD 'your_secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE hauling_qr_system TO hauling_user;
ALTER USER hauling_user CREATEDB;
\q
EOF

# Test connection
PGPASSWORD='your_secure_password_here' psql -h localhost -U hauling_user -d hauling_qr_system -c "SELECT version();"
```### Step 4: Deploy Application

```bash
# Switch to application user
sudo su - hauling

# Clone repository (replace with your actual repository URL)
git clone https://github.com/your-username/hauling-qr-system.git /opt/hauling-qr-system
cd /opt/hauling-qr-system

# Install root dependencies
npm install --production

# Install server dependencies
cd server && npm install --production && cd ..

# Install and build client
cd client && npm install && npm run build && cd ..
```

### Step 5: Environment Configuration

Create the production environment file:

```bash
# Create .env file
sudo nano /opt/hauling-qr-system/.env
```

Add the following configuration (replace values with your actual settings):

```env
# ============================================================================
# HAULING QR TRIP SYSTEM - PRODUCTION CONFIGURATION
# ============================================================================

# Environment
NODE_ENV=production

# Server Configuration
PORT=5000
HOST=0.0.0.0
BACKEND_HTTP_PORT=5000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=hauling_user
DB_PASSWORD=your_secure_password_here
DB_POOL_MAX=25
DB_POOL_MIN=5

# Security Configuration
JWT_SECRET=your_jwt_secret_64_characters_minimum_secure_random_string_here
JWT_EXPIRY=24h

# CORS Configuration (replace with your domain)
CORS_ORIGIN=https://yourdomain.com
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Client Configuration (replace with your domain)
REACT_APP_API_URL=https://yourdomain.com/api
REACT_APP_WS_URL=wss://yourdomain.com
REACT_APP_USE_HTTPS=true

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_FILE_PATH=/opt/hauling-qr-system/logs/app.log

# QR Code Configuration
QR_CODE_SIZE=200
QR_CODE_QUALITY=H
REACT_APP_QR_CAMERA_FACING=environment
REACT_APP_QR_SCAN_DELAY=500

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=/opt/hauling-qr-system/uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf
```

Set proper permissions:
```bash
sudo chown hauling:hauling /opt/hauling-qr-system/.env
sudo chmod 600 /opt/hauling-qr-system/.env
```### Step 6: Initialize Database

```bash
# Initialize database schema
cd /opt/hauling-qr-system
PGPASSWORD='your_secure_password_here' psql -h localhost -U hauling_user -d hauling_qr_system -f database/init.sql

# Run migrations if available
node database/run-migration.js
```

### Step 7: Cloudflare SSL Configuration

Choose one of the three SSL modes:

#### Option 1: Flexible SSL (Easiest Setup)
- **Server**: HTTP only (no certificates needed)
- **Cloudflare**: Handles HTTPS to visitors
- **Security**: Medium (HTTP between Cloudflare and server)

```bash
# No server-side SSL configuration needed
# Just configure Cloudflare dashboard:
# 1. Go to SSL/TLS → Overview
# 2. Set encryption mode to "Flexible"
# 3. Enable "Always Use HTTPS"
```

#### Option 2: Full SSL (Recommended)
- **Server**: HTTPS with self-signed certificate
- **Cloudflare**: HTTPS to visitors, HTTPS to server
- **Security**: High (encrypted end-to-end)

```bash
# Create self-signed certificate
sudo mkdir -p /etc/nginx/ssl
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/nginx/ssl/private.key \
    -out /etc/nginx/ssl/certificate.crt \
    -subj "/C=US/ST=State/L=City/O=Organization/CN=yourdomain.com"

# Set proper permissions
sudo chmod 600 /etc/nginx/ssl/private.key
sudo chmod 644 /etc/nginx/ssl/certificate.crt

# Configure Cloudflare:
# 1. Go to SSL/TLS → Overview
# 2. Set encryption mode to "Full"
```

#### Option 3: Full (Strict) SSL (Most Secure)
- **Server**: HTTPS with valid certificate (Let's Encrypt)
- **Cloudflare**: HTTPS to visitors, validates server certificate
- **Security**: Maximum (validated certificates)

```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Get certificate (requires domain to point to server)
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Configure Cloudflare:
# 1. Go to SSL/TLS → Overview
# 2. Set encryption mode to "Full (Strict)"
```### Step 8: Configure Nginx

Create the Nginx configuration file:

```bash
sudo nano /etc/nginx/sites-available/hauling-qr-system
```

**For Flexible SSL mode:**
```nginx
# Cloudflare IP ranges for real IP detection
set_real_ip_from ************/22;
set_real_ip_from ************/22;
set_real_ip_from **********/22;
set_real_ip_from **********/13;
set_real_ip_from **********/14;
set_real_ip_from *************/18;
set_real_ip_from **********/22;
set_real_ip_from ************/18;
set_real_ip_from ***********/15;
set_real_ip_from **********/13;
set_real_ip_from ************/20;
set_real_ip_from ************/20;
set_real_ip_from ************/20;
set_real_ip_from *************/22;
set_real_ip_from ************/17;
real_ip_header CF-Connecting-IP;

server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;

    # Serve React build files
    location / {
        root /opt/hauling-qr-system/client/build;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;

        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API routes
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket support
    location /socket.io/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```**For Full SSL mode (add SSL configuration):**
```nginx
server {
    listen 80;
    listen 443 ssl;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/nginx/ssl/certificate.crt;
    ssl_certificate_key /etc/nginx/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # ... rest of configuration same as above
}
```

Enable the site:
```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/hauling-qr-system /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test and reload
sudo nginx -t
sudo systemctl reload nginx
```

### Step 9: Configure PM2

Create PM2 ecosystem file:
```bash
sudo nano /opt/hauling-qr-system/ecosystem.config.js
```

```javascript
module.exports = {
  apps: [{
    name: 'hauling-qr-system',
    script: 'server/server.js',
    cwd: '/opt/hauling-qr-system',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: '/opt/hauling-qr-system/logs/err.log',
    out_file: '/opt/hauling-qr-system/logs/out.log',
    log_file: '/opt/hauling-qr-system/logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s'
  }]
};
```

Start the application:
```bash
# Create logs directory
sudo mkdir -p /opt/hauling-qr-system/logs
sudo chown -R hauling:hauling /opt/hauling-qr-system

# Start application
sudo -u hauling bash -c "cd /opt/hauling-qr-system && pm2 start ecosystem.config.js"
sudo -u hauling pm2 save

# Setup PM2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u hauling --hp /opt/hauling-qr-system
```## 🔧 Manual Deployment Steps

### Step 1: Initial Server Setup

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget gnupg2 software-properties-common \
    apt-transport-https ca-certificates git build-essential \
    unzip ufw fail2ban htop tree jq openssl \
    nginx postgresql postgresql-contrib \
    certbot python3-certbot-nginx redis-server supervisor

# Create application user
sudo useradd -r -s /bin/bash -d /opt/hauling-qr-system -m hauling
sudo usermod -aG sudo hauling
```

### Step 2: Install Node.js 18.x

```bash
# Add NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -

# Install Node.js and PM2
sudo apt install -y nodejs
sudo npm install -g pm2

# Verify installation
node --version  # Should show v18.x.x
npm --version
pm2 --version
```

### Step 3: Configure PostgreSQL

```bash
# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql << 'EOF'
CREATE DATABASE hauling_qr_system;
CREATE USER hauling_user WITH ENCRYPTED PASSWORD 'your_secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE hauling_qr_system TO hauling_user;
ALTER USER hauling_user CREATEDB;
\q
EOF

# Test connection
PGPASSWORD='your_secure_password_here' psql -h localhost -U hauling_user -d hauling_qr_system -c "SELECT version();"
```### Step 4: Deploy Application

```bash
# Switch to application user
sudo su - hauling

# Clone repository (replace with your actual repository URL)
git clone https://github.com/your-username/hauling-qr-system.git /opt/hauling-qr-system
cd /opt/hauling-qr-system

# Install root dependencies
npm install --production

# Install server dependencies
cd server && npm install --production && cd ..

# Install and build client
cd client && npm install && npm run build && cd ..
```

### Step 5: Configure Environment

Create production environment file:

```bash
# Create .env file
sudo nano /opt/hauling-qr-system/.env
```

Add the following configuration (replace values with your actual settings):

```env
# ============================================================================
# HAULING QR TRIP SYSTEM - PRODUCTION CONFIGURATION
# ============================================================================

# Environment
NODE_ENV=production

# Server Configuration
PORT=5000
HOST=0.0.0.0
BACKEND_HTTP_PORT=5000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=hauling_user
DB_PASSWORD=your_secure_password_here
DB_POOL_MAX=25
DB_POOL_MIN=5

# Security Configuration
JWT_SECRET=your_jwt_secret_64_characters_minimum_secure_random_string_here
JWT_EXPIRY=24h

# CORS Configuration
CORS_ORIGIN=https://yourdomain.com
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Client Configuration
REACT_APP_API_URL=https://yourdomain.com/api
REACT_APP_WS_URL=wss://yourdomain.com
REACT_APP_USE_HTTPS=true

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_FILE_PATH=/opt/hauling-qr-system/logs/app.log

# QR Code Configuration
QR_CODE_SIZE=200
QR_CODE_QUALITY=H
REACT_APP_QR_CAMERA_FACING=environment
REACT_APP_QR_SCAN_DELAY=500

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=/opt/hauling-qr-system/uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf
```

Set proper permissions:
```bash
sudo chown hauling:hauling /opt/hauling-qr-system/.env
sudo chmod 600 /opt/hauling-qr-system/.env
```### Step 6: Initialize Database

```bash
# Initialize database schema
cd /opt/hauling-qr-system
PGPASSWORD='your_secure_password_here' psql -h localhost -U hauling_user -d hauling_qr_system -f database/init.sql

# Run migrations if available
node database/run-migration.js
```

### Step 7: Configure Cloudflare SSL

Choose one of the three SSL modes:

#### Option 1: Flexible SSL (Easiest)
- **Server**: HTTP only
- **Cloudflare to Client**: HTTPS
- **Setup**: No server certificates needed

1. Go to Cloudflare Dashboard → SSL/TLS → Overview
2. Set encryption mode to "Flexible"
3. Enable "Always Use HTTPS"

#### Option 2: Full SSL (Recommended)
- **Server**: HTTPS with self-signed certificate
- **Cloudflare to Client**: HTTPS
- **Setup**: Generate self-signed certificate

```bash
# Create SSL directory
sudo mkdir -p /etc/nginx/ssl

# Generate self-signed certificate
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/nginx/ssl/private.key \
    -out /etc/nginx/ssl/certificate.crt \
    -subj "/C=US/ST=State/L=City/O=Organization/CN=yourdomain.com"

# Set proper permissions
sudo chmod 600 /etc/nginx/ssl/private.key
sudo chmod 644 /etc/nginx/ssl/certificate.crt
```

Then set Cloudflare to "Full" mode.

#### Option 3: Full (Strict) SSL (Most Secure)
- **Server**: HTTPS with valid certificate
- **Cloudflare to Client**: HTTPS
- **Setup**: Use Let's Encrypt or purchased certificate

```bash
# Using Let's Encrypt (requires domain to be pointed to server)
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

Then set Cloudflare to "Full (Strict)" mode.### Step 8: Configure Nginx

Create Nginx configuration:

```bash
sudo nano /etc/nginx/sites-available/hauling-qr-system
```

Add the following configuration (adjust for your SSL mode):

```nginx
# Cloudflare IP ranges for real IP detection
set_real_ip_from ************/22;
set_real_ip_from ************/22;
set_real_ip_from **********/22;
set_real_ip_from **********/13;
set_real_ip_from **********/14;
set_real_ip_from *************/18;
set_real_ip_from **********/22;
set_real_ip_from ************/18;
set_real_ip_from ***********/15;
set_real_ip_from **********/13;
set_real_ip_from ************/20;
set_real_ip_from ************/20;
set_real_ip_from ************/20;
set_real_ip_from *************/22;
set_real_ip_from ************/17;
real_ip_header CF-Connecting-IP;

server {
    listen 80;
    # Add this line for Full SSL mode:
    # listen 443 ssl;
    
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration (for Full SSL mode only)
    # ssl_certificate /etc/nginx/ssl/certificate.crt;
    # ssl_certificate_key /etc/nginx/ssl/private.key;
    # ssl_protocols TLSv1.2 TLSv1.3;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;

    # Serve React build files
    location / {
        root /opt/hauling-qr-system/client/build;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;

        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API routes with rate limiting
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # WebSocket support
    location /socket.io/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Health check
    location /health {
        proxy_pass http://localhost:5000/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/hauling-qr-system /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl reload nginx
```### Step 9: Configure PM2

Create PM2 ecosystem file:

```bash
sudo nano /opt/hauling-qr-system/ecosystem.config.js
```

Add the following configuration:

```javascript
module.exports = {
  apps: [{
    name: 'hauling-qr-system',
    script: 'server/server.js',
    cwd: '/opt/hauling-qr-system',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: '/opt/hauling-qr-system/logs/err.log',
    out_file: '/opt/hauling-qr-system/logs/out.log',
    log_file: '/opt/hauling-qr-system/logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    watch: false,
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s'
  }]
};
```

Start the application:

```bash
# Create logs directory
sudo mkdir -p /opt/hauling-qr-system/logs
sudo chown -R hauling:hauling /opt/hauling-qr-system

# Start application as hauling user
sudo -u hauling bash -c "cd /opt/hauling-qr-system && pm2 start ecosystem.config.js --env production"
sudo -u hauling pm2 save

# Setup PM2 startup script
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u hauling --hp /opt/hauling-qr-system
```### Step 10: Configure Security

#### Firewall Setup
```bash
# Reset UFW
sudo ufw --force reset

# Set default policies
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH, HTTP, and HTTPS
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Enable firewall
sudo ufw --force enable
sudo ufw status
```

#### Fail2Ban Configuration
```bash
# Create custom jail configuration
sudo nano /etc/fail2ban/jail.local
```

Add the following:
```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
maxretry = 10
```

```bash
# Restart and enable fail2ban
sudo systemctl restart fail2ban
sudo systemctl enable fail2ban
```

### Step 11: Create Admin User

Create admin user script:

```bash
cat > /tmp/create-admin.js << 'EOF'
const bcrypt = require('bcryptjs');
const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'hauling_qr_system',
  user: 'hauling_user',
  password: 'your_secure_password_here'
});

async function createAdmin() {
  try {
    const hashedPassword = await bcrypt.hash('your_admin_password', 12);
    
    const result = await pool.query(
      'INSERT INTO users (username, email, password_hash, full_name, role, status) VALUES ($1, $2, $3, $4, $5, $6) ON CONFLICT (email) DO NOTHING RETURNING id',
      ['admin', '<EMAIL>', hashedPassword, 'System Administrator', 'admin', 'active']
    );
    
    if (result.rows.length > 0) {
      console.log('Admin user created successfully');
    } else {
      console.log('Admin user already exists');
    }
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await pool.end();
  }
}

createAdmin();
EOF

# Run the script
cd /opt/hauling-qr-system
sudo -u hauling node /tmp/create-admin.js
rm /tmp/create-admin.js
```## 🔍 Verification and Testing

### Service Status Check
```bash
# Check all services
sudo systemctl status nginx postgresql redis-server
sudo -u hauling pm2 status

# Test database connection
PGPASSWORD='your_password' psql -h localhost -U hauling_user -d hauling_qr_system -c "SELECT version();"

# Test API endpoint
curl -k http://localhost:5000/health

# Test application through Nginx
curl -k http://yourdomain.com/health
```

### Performance Testing
```bash
# Install Apache Bench
sudo apt install -y apache2-utils

# Test API performance
ab -n 100 -c 10 http://localhost:5000/health

# Test static file serving
ab -n 100 -c 10 https://yourdomain.com/
```

## 🌐 Cloudflare Configuration

### DNS Settings
1. **A Record**: Point your domain to server IP
2. **CNAME Record**: Point www to your domain
3. **Proxy Status**: Enable (orange cloud) for both records

### SSL/TLS Settings
1. **Encryption Mode**: Set according to your chosen method
   - Flexible: HTTP to server
   - Full: HTTPS with self-signed cert
   - Full (Strict): HTTPS with valid cert
2. **Always Use HTTPS**: Enable
3. **HSTS**: Enable for additional security

### Performance Settings
1. **Auto Minify**: Enable for HTML, CSS, JS
2. **Brotli**: Enable
3. **Caching Level**: Standard
4. **Browser Cache TTL**: 4 hours

### Security Settings
1. **Security Level**: Medium
2. **Challenge Passage**: 30 minutes
3. **Browser Integrity Check**: Enable
4. **Bot Fight Mode**: Enable

## 📊 Monitoring and Maintenance

### Log Monitoring
```bash
# Application logs
sudo -u hauling pm2 logs hauling-qr-system

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# System logs
sudo journalctl -u nginx -f
sudo journalctl -u postgresql -f
```

### Health Check Script
Create automated health monitoring:

```bash
sudo nano /opt/hauling-qr-system/scripts/health-check.sh
```

```bash
#!/bin/bash
LOG_FILE="/var/log/hauling-qr-health.log"

# Check services
for service in nginx postgresql redis-server; do
    if systemctl is-active --quiet $service; then
        echo "$(date): $service is running" >> $LOG_FILE
    else
        echo "$(date): $service is NOT running" >> $LOG_FILE
        systemctl restart $service
    fi
done

# Check PM2 application
if pm2 list | grep -q "hauling-qr-system.*online"; then
    echo "$(date): Application is running" >> $LOG_FILE
else
    echo "$(date): Application is NOT running" >> $LOG_FILE
    pm2 restart hauling-qr-system
fi

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): WARNING - Disk usage is ${DISK_USAGE}%" >> $LOG_FILE
fi
```

```bash
# Make executable and add to crontab
sudo chmod +x /opt/hauling-qr-system/scripts/health-check.sh
(crontab -l 2>/dev/null; echo "*/5 * * * * /opt/hauling-qr-system/scripts/health-check.sh") | crontab -
```### Database Backup Setup
```bash
# Create backup script
sudo nano /opt/hauling-qr-system/scripts/backup-db.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/opt/hauling-qr-system/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="hauling_qr_system"
DB_USER="hauling_user"
DB_PASSWORD="your_secure_password_here"

mkdir -p $BACKUP_DIR

# Create backup
PGPASSWORD="$DB_PASSWORD" pg_dump -h localhost -U $DB_USER $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/backup_$DATE.sql

# Keep only last 7 days of backups
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete

echo "$(date): Database backup completed: backup_$DATE.sql.gz" >> /var/log/hauling-qr-backup.log
```

```bash
# Make executable and schedule daily backups
sudo chmod +x /opt/hauling-qr-system/scripts/backup-db.sh
sudo chown hauling:hauling /opt/hauling-qr-system/scripts/backup-db.sh

# Add to crontab for daily backups at 2 AM
(sudo -u hauling crontab -l 2>/dev/null; echo "0 2 * * * /opt/hauling-qr-system/scripts/backup-db.sh") | sudo -u hauling crontab -
```

### Log Rotation Setup
```bash
# Create logrotate configuration
sudo nano /etc/logrotate.d/hauling-qr-system
```

```
/opt/hauling-qr-system/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 hauling hauling
    postrotate
        sudo -u hauling pm2 reloadLogs
    endscript
}

/var/log/nginx/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data adm
    postrotate
        systemctl reload nginx
    endscript
}
```

## 🚨 Troubleshooting Guide

### Common Issues and Solutions

#### 1. Application Not Starting
**Symptoms**: PM2 shows app as stopped or errored
```bash
# Check logs
sudo -u hauling pm2 logs hauling-qr-system

# Check environment variables
cat /opt/hauling-qr-system/.env

# Restart application
sudo -u hauling pm2 restart hauling-qr-system

# Check Node.js version
node --version  # Should be v18.x.x
```

#### 2. Database Connection Issues
**Symptoms**: Application logs show database connection errors
```bash
# Test database connection
PGPASSWORD='your_password' psql -h localhost -U hauling_user -d hauling_qr_system

# Check PostgreSQL status
sudo systemctl status postgresql

# Check PostgreSQL logs
sudo tail -f /var/log/postgresql/postgresql-15-main.log

# Restart PostgreSQL
sudo systemctl restart postgresql
```

#### 3. Nginx/SSL Issues
**Symptoms**: 502 Bad Gateway, SSL certificate errors
```bash
# Test Nginx configuration
sudo nginx -t

# Check Nginx logs
sudo tail -f /var/log/nginx/error.log

# Restart Nginx
sudo systemctl restart nginx

# Check SSL certificate (for Full SSL mode)
openssl x509 -in /etc/nginx/ssl/certificate.crt -text -noout
```

#### 4. Cloudflare SSL Issues
**Symptoms**: SSL handshake failures, mixed content warnings

**Solutions**:
- Verify SSL mode matches server configuration
- Check DNS propagation: `nslookup yourdomain.com`
- Ensure Cloudflare proxy is enabled (orange cloud)
- Clear Cloudflare cache if needed

#### 5. Performance Issues
**Symptoms**: Slow response times, high memory usage
```bash
# Monitor system resources
htop

# Check PM2 cluster status
sudo -u hauling pm2 show hauling-qr-system

# Monitor database performance
PGPASSWORD='your_password' psql -h localhost -U hauling_user -d hauling_qr_system -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;"

# Restart application with fresh memory
sudo -u hauling pm2 restart hauling-qr-system
```## 🔧 Useful Commands Reference

### Application Management
```bash
# PM2 Commands
sudo -u hauling pm2 status                    # Check application status
sudo -u hauling pm2 logs hauling-qr-system    # View logs
sudo -u hauling pm2 restart hauling-qr-system # Restart application
sudo -u hauling pm2 reload hauling-qr-system  # Reload without downtime
sudo -u hauling pm2 stop hauling-qr-system    # Stop application
sudo -u hauling pm2 delete hauling-qr-system  # Delete from PM2
sudo -u hauling pm2 monit                     # Monitor resources

# Application Updates
cd /opt/hauling-qr-system
git pull origin main                           # Pull latest code
sudo -u hauling npm install --production       # Update dependencies
cd client && sudo -u hauling npm run build    # Rebuild frontend
sudo -u hauling pm2 restart hauling-qr-system # Restart application
```

### Service Management
```bash
# System Services
sudo systemctl status nginx postgresql redis-server  # Check services
sudo systemctl restart nginx                         # Restart Nginx
sudo systemctl reload nginx                          # Reload Nginx config
sudo systemctl restart postgresql                    # Restart PostgreSQL
sudo systemctl restart redis-server                  # Restart Redis

# Firewall Management
sudo ufw status                                      # Check firewall status
sudo ufw allow 8080/tcp                            # Allow specific port
sudo ufw delete allow 8080/tcp                     # Remove rule
sudo ufw reload                                     # Reload firewall

# Fail2Ban Management
sudo fail2ban-client status                         # Check fail2ban status
sudo fail2ban-client status nginx-limit-req        # Check specific jail
sudo fail2ban-client unban IP_ADDRESS              # Unban IP address
```

### Database Management
```bash
# Database Operations
PGPASSWORD='password' psql -h localhost -U hauling_user -d hauling_qr_system

# Common SQL Commands
SELECT version();                                   # Check PostgreSQL version
\dt                                                # List tables
\d table_name                                      # Describe table structure
SELECT COUNT(*) FROM users;                        # Count records
VACUUM ANALYZE;                                    # Optimize database

# Backup and Restore
pg_dump -h localhost -U hauling_user hauling_qr_system > backup.sql
psql -h localhost -U hauling_user -d hauling_qr_system < backup.sql
```

### Log Management
```bash
# View Logs
sudo tail -f /var/log/nginx/access.log            # Nginx access logs
sudo tail -f /var/log/nginx/error.log             # Nginx error logs
sudo journalctl -u nginx -f                       # Nginx system logs
sudo journalctl -u postgresql -f                  # PostgreSQL logs
sudo -u hauling pm2 logs hauling-qr-system --lines 100  # Application logs

# Log Analysis
sudo grep "ERROR" /var/log/nginx/error.log        # Find errors
sudo awk '{print $1}' /var/log/nginx/access.log | sort | uniq -c | sort -nr  # Top IPs
```

## 📋 Security Checklist

### Pre-Deployment Security
- [ ] Server is updated with latest security patches
- [ ] SSH key-based authentication is configured
- [ ] Root login is disabled
- [ ] Strong passwords are used for all accounts
- [ ] Firewall is properly configured

### Application Security
- [ ] Environment variables contain secure, random values
- [ ] JWT secret is at least 64 characters long
- [ ] Database passwords are strong and unique
- [ ] CORS is properly configured for production
- [ ] Rate limiting is enabled
- [ ] Input validation is implemented

### Infrastructure Security
- [ ] Nginx security headers are configured
- [ ] SSL/TLS is properly configured
- [ ] Fail2Ban is active and monitoring
- [ ] Log rotation is configured
- [ ] Regular backups are scheduled
- [ ] Monitoring and alerting is set up

### Cloudflare Security
- [ ] SSL encryption mode is appropriate
- [ ] Security level is set to Medium or High
- [ ] Bot protection is enabled
- [ ] Rate limiting rules are configured
- [ ] Page rules are optimized

## 🔄 Maintenance Tasks

### Daily Tasks
- Monitor application logs for errors
- Check system resource usage (CPU, memory, disk)
- Verify all services are running
- Review security logs for suspicious activity

### Weekly Tasks
```bash
# System updates
sudo apt update && sudo apt list --upgradable
sudo apt upgrade -y

# Log review
sudo logrotate -f /etc/logrotate.d/hauling-qr-system

# Security scan
sudo fail2ban-client status
```

### Monthly Tasks
```bash
# Dependency updates
cd /opt/hauling-qr-system
sudo -u hauling npm audit
sudo -u hauling npm update

# Database maintenance
PGPASSWORD='password' psql -h localhost -U hauling_user -d hauling_qr_system -c "VACUUM ANALYZE;"

# Backup verification
ls -la /opt/hauling-qr-system/backups/
```

### Quarterly Tasks
- Security audit and penetration testing
- Performance optimization review
- SSL certificate renewal (if not automated)
- Disaster recovery testing
- Documentation updates## 🚀 Production Optimization

### Performance Tuning

#### Nginx Optimization
```bash
# Edit Nginx main configuration
sudo nano /etc/nginx/nginx.conf
```

Add these optimizations:
```nginx
# Worker processes (set to number of CPU cores)
worker_processes auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # Basic optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Client body size
    client_max_body_size 10M;
    client_body_buffer_size 128k;
    
    # Timeouts
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;
}
```

#### PostgreSQL Optimization
```bash
# Edit PostgreSQL configuration
sudo nano /etc/postgresql/15/main/postgresql.conf
```

Key optimizations:
```ini
# Memory settings (adjust based on available RAM)
shared_buffers = 256MB                    # 25% of RAM
effective_cache_size = 1GB               # 75% of RAM
work_mem = 4MB                           # Per connection
maintenance_work_mem = 64MB              # For maintenance operations

# Connection settings
max_connections = 100                    # Adjust based on needs
superuser_reserved_connections = 3

# Checkpoint settings
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Query planner
random_page_cost = 1.1                   # For SSD storage
effective_io_concurrency = 200           # For SSD storage
```

```bash
# Restart PostgreSQL to apply changes
sudo systemctl restart postgresql
```

#### PM2 Optimization
```bash
# Update ecosystem.config.js for production
sudo nano /opt/hauling-qr-system/ecosystem.config.js
```

```javascript
module.exports = {
  apps: [{
    name: 'hauling-qr-system',
    script: 'server/server.js',
    cwd: '/opt/hauling-qr-system',
    instances: 'max',                    // Use all CPU cores
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 5000,
      UV_THREADPOOL_SIZE: 128           // Increase thread pool
    },
    node_args: [
      '--max-old-space-size=2048',      // Increase heap size
      '--optimize-for-size'             // Optimize for memory
    ],
    max_memory_restart: '2G',           // Restart if memory exceeds 2GB
    min_uptime: '10s',
    max_restarts: 10,
    restart_delay: 4000,
    kill_timeout: 5000,
    listen_timeout: 8000,
    
    // Advanced PM2 features
    merge_logs: true,
    time: true,
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    
    // Health monitoring
    health_check_grace_period: 10000,
    health_check_fatal_exceptions: true
  }]
};
```

### Monitoring and Alerting

#### System Monitoring with htop and iotop
```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs

# Monitor system resources
htop                    # CPU and memory usage
sudo iotop             # Disk I/O usage
sudo nethogs           # Network usage by process
```

#### Application Performance Monitoring
Create advanced monitoring script:

```bash
sudo nano /opt/hauling-qr-system/scripts/performance-monitor.sh
```

```bash
#!/bin/bash
LOG_FILE="/var/log/hauling-qr-performance.log"
ALERT_EMAIL="<EMAIL>"

# Function to send alerts
send_alert() {
    local subject="$1"
    local message="$2"
    echo "$message" | mail -s "$subject" "$ALERT_EMAIL"
}

# Check CPU usage
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
    echo "$(date): HIGH CPU USAGE: ${CPU_USAGE}%" >> $LOG_FILE
    send_alert "High CPU Usage Alert" "CPU usage is at ${CPU_USAGE}%"
fi

# Check memory usage
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.2f", $3*100/$2}')
if (( $(echo "$MEMORY_USAGE > 85" | bc -l) )); then
    echo "$(date): HIGH MEMORY USAGE: ${MEMORY_USAGE}%" >> $LOG_FILE
    send_alert "High Memory Usage Alert" "Memory usage is at ${MEMORY_USAGE}%"
fi

# Check disk usage
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 85 ]; then
    echo "$(date): HIGH DISK USAGE: ${DISK_USAGE}%" >> $LOG_FILE
    send_alert "High Disk Usage Alert" "Disk usage is at ${DISK_USAGE}%"
fi

# Check application response time
RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:5000/health)
if (( $(echo "$RESPONSE_TIME > 2.0" | bc -l) )); then
    echo "$(date): SLOW RESPONSE TIME: ${RESPONSE_TIME}s" >> $LOG_FILE
    send_alert "Slow Response Time Alert" "API response time is ${RESPONSE_TIME}s"
fi

# Check database connections
DB_CONNECTIONS=$(PGPASSWORD='your_password' psql -h localhost -U hauling_user -d hauling_qr_system -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';")
if [ $DB_CONNECTIONS -gt 50 ]; then
    echo "$(date): HIGH DB CONNECTIONS: $DB_CONNECTIONS" >> $LOG_FILE
    send_alert "High Database Connections" "Active database connections: $DB_CONNECTIONS"
fi

# Log performance metrics
echo "$(date): CPU: ${CPU_USAGE}%, Memory: ${MEMORY_USAGE}%, Disk: ${DISK_USAGE}%, Response: ${RESPONSE_TIME}s, DB Conn: $DB_CONNECTIONS" >> $LOG_FILE
```

```bash
# Make executable and schedule
sudo chmod +x /opt/hauling-qr-system/scripts/performance-monitor.sh
(crontab -l 2>/dev/null; echo "*/10 * * * * /opt/hauling-qr-system/scripts/performance-monitor.sh") | crontab -
```

## 🔐 Advanced Security Configuration

### SSL Security Headers
Add to Nginx configuration:

```nginx
# Security headers
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' wss: https:;" always;
add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;
```

### Rate Limiting Enhancement
```nginx
# Enhanced rate limiting
http {
    # Define rate limit zones
    limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=api:10m rate=20r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=upload:10m rate=2r/s;
    
    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    limit_conn conn_limit_per_ip 20;
}

server {
    # Apply rate limits to different endpoints
    location /api/auth/ {
        limit_req zone=auth burst=10 nodelay;
        limit_req_status 429;
    }
    
    location /api/upload {
        limit_req zone=upload burst=5 nodelay;
        client_max_body_size 10M;
    }
    
    location /api/ {
        limit_req zone=api burst=50 nodelay;
    }
    
    location / {
        limit_req zone=general burst=20 nodelay;
    }
}
```

### Intrusion Detection
```bash
# Install and configure AIDE (Advanced Intrusion Detection Environment)
sudo apt install -y aide

# Initialize AIDE database
sudo aideinit

# Create daily check script
sudo nano /etc/cron.daily/aide-check
```

```bash
#!/bin/bash
/usr/bin/aide --check > /var/log/aide-check.log 2>&1
if [ $? -ne 0 ]; then
    mail -s "AIDE Integrity Check Failed" <EMAIL> < /var/log/aide-check.log
fi
```

```bash
sudo chmod +x /etc/cron.daily/aide-check
```

## 📊 Analytics and Reporting

### Application Analytics
Create analytics dashboard script:

```bash
sudo nano /opt/hauling-qr-system/scripts/generate-report.sh
```

```bash
#!/bin/bash
REPORT_DATE=$(date +%Y-%m-%d)
REPORT_FILE="/opt/hauling-qr-system/reports/daily-report-$REPORT_DATE.html"

mkdir -p /opt/hauling-qr-system/reports

# Generate HTML report
cat > $REPORT_FILE << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Hauling QR System - Daily Report $REPORT_DATE</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .metric { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .alert { background: #ffebee; border-left: 4px solid #f44336; }
        .success { background: #e8f5e8; border-left: 4px solid #4caf50; }
    </style>
</head>
<body>
    <h1>Hauling QR System - Daily Report</h1>
    <h2>Date: $REPORT_DATE</h2>
    
    <div class="metric success">
        <h3>System Status</h3>
        <p>Application: $(sudo -u hauling pm2 list | grep hauling-qr-system | awk '{print $10}')</p>
        <p>Nginx: $(systemctl is-active nginx)</p>
        <p>PostgreSQL: $(systemctl is-active postgresql)</p>
        <p>Redis: $(systemctl is-active redis-server)</p>
    </div>
    
    <div class="metric">
        <h3>Performance Metrics</h3>
        <p>CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}')</p>
        <p>Memory Usage: $(free | awk 'NR==2{printf "%.1f%%", $3*100/$2}')</p>
        <p>Disk Usage: $(df / | awk 'NR==2 {print $5}')</p>
        <p>Load Average: $(uptime | awk -F'load average:' '{print $2}')</p>
    </div>
    
    <div class="metric">
        <h3>Database Statistics</h3>
        <p>Active Connections: $(PGPASSWORD='your_password' psql -h localhost -U hauling_user -d hauling_qr_system -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';")</p>
        <p>Database Size: $(PGPASSWORD='your_password' psql -h localhost -U hauling_user -d hauling_qr_system -t -c "SELECT pg_size_pretty(pg_database_size('hauling_qr_system'));")</p>
    </div>
    
    <div class="metric">
        <h3>Recent Errors</h3>
        <pre>$(sudo tail -20 /var/log/nginx/error.log)</pre>
    </div>
    
    <p><em>Report generated on $(date)</em></p>
</body>
</html>
EOF

echo "Daily report generated: $REPORT_FILE"
```

```bash
# Schedule daily reports
sudo chmod +x /opt/hauling-qr-system/scripts/generate-report.sh
(crontab -l 2>/dev/null; echo "0 6 * * * /opt/hauling-qr-system/scripts/generate-report.sh") | crontab -
```## 🔄 Disaster Recovery and Backup Strategy

### Complete Backup Solution

#### Full System Backup Script
```bash
sudo nano /opt/hauling-qr-system/scripts/full-backup.sh
```

```bash
#!/bin/bash
BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_ROOT="/opt/hauling-qr-system/backups"
FULL_BACKUP_DIR="$BACKUP_ROOT/full-backup-$BACKUP_DATE"

mkdir -p "$FULL_BACKUP_DIR"

echo "Starting full system backup at $(date)"

# 1. Database backup
echo "Backing up database..."
PGPASSWORD='your_password' pg_dump -h localhost -U hauling_user hauling_qr_system > "$FULL_BACKUP_DIR/database.sql"

# 2. Application files backup
echo "Backing up application files..."
tar -czf "$FULL_BACKUP_DIR/application.tar.gz" \
    --exclude='node_modules' \
    --exclude='logs' \
    --exclude='backups' \
    /opt/hauling-qr-system/

# 3. Configuration files backup
echo "Backing up configuration files..."
mkdir -p "$FULL_BACKUP_DIR/config"
cp /etc/nginx/sites-available/hauling-qr-system "$FULL_BACKUP_DIR/config/"
cp /etc/fail2ban/jail.local "$FULL_BACKUP_DIR/config/" 2>/dev/null || true
cp /etc/logrotate.d/hauling-qr-system "$FULL_BACKUP_DIR/config/" 2>/dev/null || true

# 4. SSL certificates backup (if using Full SSL)
if [ -d "/etc/nginx/ssl" ]; then
    echo "Backing up SSL certificates..."
    cp -r /etc/nginx/ssl "$FULL_BACKUP_DIR/config/"
fi

# 5. PM2 configuration backup
echo "Backing up PM2 configuration..."
sudo -u hauling pm2 save --force
cp /opt/hauling-qr-system/.pm2/dump.pm2 "$FULL_BACKUP_DIR/config/" 2>/dev/null || true

# 6. Create restore script
cat > "$FULL_BACKUP_DIR/restore.sh" << 'RESTORE_EOF'
#!/bin/bash
echo "Starting system restore..."

# Restore database
echo "Restoring database..."
PGPASSWORD='your_password' psql -h localhost -U hauling_user -d hauling_qr_system < database.sql

# Restore application files
echo "Restoring application files..."
cd /
sudo tar -xzf "$PWD/application.tar.gz"

# Restore configurations
echo "Restoring configurations..."
sudo cp config/hauling-qr-system /etc/nginx/sites-available/
sudo cp config/jail.local /etc/fail2ban/ 2>/dev/null || true
sudo cp config/hauling-qr-system /etc/logrotate.d/ 2>/dev/null || true

# Restore SSL certificates
if [ -d "config/ssl" ]; then
    sudo cp -r config/ssl /etc/nginx/
fi

# Restart services
sudo systemctl restart nginx postgresql fail2ban
sudo -u hauling pm2 resurrect

echo "Restore completed. Please verify all services are running."
RESTORE_EOF

chmod +x "$FULL_BACKUP_DIR/restore.sh"

# Compress the entire backup
echo "Compressing backup..."
cd "$BACKUP_ROOT"
tar -czf "full-backup-$BACKUP_DATE.tar.gz" "full-backup-$BACKUP_DATE/"
rm -rf "full-backup-$BACKUP_DATE/"

# Clean old backups (keep last 3 full backups)
ls -t full-backup-*.tar.gz | tail -n +4 | xargs rm -f

echo "Full backup completed: full-backup-$BACKUP_DATE.tar.gz"
echo "Backup size: $(du -h full-backup-$BACKUP_DATE.tar.gz | cut -f1)"
```

```bash
# Schedule weekly full backups
sudo chmod +x /opt/hauling-qr-system/scripts/full-backup.sh
sudo chown hauling:hauling /opt/hauling-qr-system/scripts/full-backup.sh
(sudo -u hauling crontab -l 2>/dev/null; echo "0 3 * * 0 /opt/hauling-qr-system/scripts/full-backup.sh") | sudo -u hauling crontab -
```

### Disaster Recovery Plan

#### Recovery Procedures

**1. Complete System Failure Recovery**
```bash
# On new Ubuntu 24.04 server:
# 1. Run the auto-deployment script
./deploy-hauling-qr-ubuntu.sh

# 2. Stop the application
sudo -u hauling pm2 stop hauling-qr-system

# 3. Extract and run restore script
cd /opt/hauling-qr-system/backups
tar -xzf full-backup-YYYYMMDD_HHMMSS.tar.gz
cd full-backup-YYYYMMDD_HHMMSS
./restore.sh

# 4. Update DNS and Cloudflare settings
# 5. Verify all services are running
```

**2. Database Corruption Recovery**
```bash
# Stop application
sudo -u hauling pm2 stop hauling-qr-system

# Drop and recreate database
sudo -u postgres psql << 'EOF'
DROP DATABASE hauling_qr_system;
CREATE DATABASE hauling_qr_system;
GRANT ALL PRIVILEGES ON DATABASE hauling_qr_system TO hauling_user;
EOF

# Restore from latest backup
PGPASSWORD='your_password' psql -h localhost -U hauling_user -d hauling_qr_system < /opt/hauling-qr-system/backups/backup_YYYYMMDD_HHMMSS.sql

# Restart application
sudo -u hauling pm2 start hauling-qr-system
```

**3. Application Corruption Recovery**
```bash
# Backup current state
mv /opt/hauling-qr-system /opt/hauling-qr-system-corrupted

# Clone fresh copy
git clone https://github.com/your-repo/hauling-qr-system.git /opt/hauling-qr-system

# Restore configuration
cp /opt/hauling-qr-system-corrupted/.env /opt/hauling-qr-system/
cp /opt/hauling-qr-system-corrupted/ecosystem.config.js /opt/hauling-qr-system/

# Reinstall dependencies and restart
cd /opt/hauling-qr-system
sudo -u hauling npm install --production
cd client && sudo -u hauling npm install && sudo -u hauling npm run build
sudo -u hauling pm2 restart hauling-qr-system
```

## 🎯 Final Deployment Checklist

### Pre-Go-Live Checklist
- [ ] **Server Setup**
  - [ ] Ubuntu 24.04 LTS installed and updated
  - [ ] All required packages installed
  - [ ] Application user created with proper permissions
  - [ ] Firewall configured and enabled

- [ ] **Application Deployment**
  - [ ] Code deployed from repository
  - [ ] Dependencies installed (server and client)
  - [ ] Frontend built for production
  - [ ] Environment variables configured
  - [ ] Database schema initialized

- [ ] **Database Configuration**
  - [ ] PostgreSQL installed and running
  - [ ] Database and user created
  - [ ] Connection tested successfully
  - [ ] Initial data seeded (if required)

- [ ] **Web Server Setup**
  - [ ] Nginx installed and configured
  - [ ] SSL certificates configured (if using Full SSL)
  - [ ] Security headers enabled
  - [ ] Rate limiting configured
  - [ ] Gzip compression enabled

- [ ] **Process Management**
  - [ ] PM2 installed and configured
  - [ ] Application running in cluster mode
  - [ ] Auto-restart on failure enabled
  - [ ] Startup script configured

- [ ] **Security Configuration**
  - [ ] Firewall rules applied
  - [ ] Fail2Ban configured and running
  - [ ] Strong passwords used everywhere
  - [ ] JWT secret is secure and random
  - [ ] CORS properly configured

- [ ] **Cloudflare Setup**
  - [ ] Domain DNS pointed to server
  - [ ] Cloudflare proxy enabled
  - [ ] SSL mode configured correctly
  - [ ] Security settings optimized
  - [ ] Caching rules configured

- [ ] **Monitoring and Backups**
  - [ ] Health check script configured
  - [ ] Performance monitoring enabled
  - [ ] Log rotation configured
  - [ ] Database backups scheduled
  - [ ] Full system backup scheduled

### Post-Go-Live Checklist
- [ ] **Functionality Testing**
  - [ ] Application loads correctly
  - [ ] User registration/login works
  - [ ] QR code scanning functional
  - [ ] Trip management features work
  - [ ] Dashboard displays data correctly
  - [ ] Mobile responsiveness verified

- [ ] **Performance Testing**
  - [ ] Page load times acceptable (<3 seconds)
  - [ ] API response times good (<500ms)
  - [ ] Database queries optimized
  - [ ] Memory usage within limits
  - [ ] CPU usage reasonable

- [ ] **Security Verification**
  - [ ] SSL certificate valid and trusted
  - [ ] Security headers present
  - [ ] Rate limiting working
  - [ ] No sensitive data exposed
  - [ ] Admin account secured

- [ ] **Monitoring Setup**
  - [ ] Health checks running
  - [ ] Log monitoring active
  - [ ] Performance alerts configured
  - [ ] Backup verification successful
  - [ ] Documentation updated

## 📞 Support and Resources

### Getting Help
- **Documentation**: This deployment guide
- **Application Logs**: `/opt/hauling-qr-system/logs/`
- **System Logs**: `/var/log/nginx/`, `/var/log/postgresql/`
- **Health Check**: `http://yourdomain.com/health`

### Useful Resources
- **Ubuntu 24.04 Documentation**: https://help.ubuntu.com/
- **Nginx Documentation**: https://nginx.org/en/docs/
- **PostgreSQL Documentation**: https://www.postgresql.org/docs/
- **PM2 Documentation**: https://pm2.keymetrics.io/docs/
- **Cloudflare Documentation**: https://developers.cloudflare.com/

### Emergency Contacts
- **System Administrator**: <EMAIL>
- **Technical Support**: <EMAIL>
- **Emergency Hotline**: +1-XXX-XXX-XXXX

---

## 🎉 Congratulations!

Your Hauling QR Trip Management System is now successfully deployed on Ubuntu 24.04 with Cloudflare SSL integration. The system is production-ready with:

- ✅ **High Availability**: PM2 cluster mode with auto-restart
- ✅ **Security**: Firewall, Fail2Ban, SSL, and security headers
- ✅ **Performance**: Nginx optimization, database tuning, and caching
- ✅ **Monitoring**: Health checks, performance monitoring, and alerting
- ✅ **Backup**: Automated database and full system backups
- ✅ **Scalability**: Ready for horizontal scaling when needed

Remember to:
1. Change default admin password after first login
2. Monitor system performance regularly
3. Keep the system updated with security patches
4. Test backup and recovery procedures periodically

**Happy hauling! 🚛**