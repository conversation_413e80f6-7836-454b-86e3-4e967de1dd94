/**
 * Simple test for cleanup endpoints
 */

const axios = require('axios');

async function testCleanupEndpoints() {
  const baseUrl = 'http://localhost:5000';
  
  console.log('Testing cleanup endpoints...');
  
  try {
    // Test analyze endpoint without auth first
    try {
      const analyzeResponse = await axios.post(`${baseUrl}/api/system-health/cleanup/analyze`);
      console.log('✅ Analyze endpoint accessible:', analyzeResponse.status);
    } catch (error) {
      const status = error.response?.status;
      const message = error.response?.data?.message || error.message;
      
      if (status === 500) {
        console.log(`❌ Analyze endpoint: 500 Internal Server Error - ${message}`);
      } else if (status === 401 || status === 403) {
        console.log(`✅ Analyze endpoint: ${status} (Auth required - endpoint exists)`);
      } else if (status === 404) {
        console.log(`❌ Analyze endpoint: 404 Not Found - endpoint missing`);
      } else {
        console.log(`⚠️ Analyze endpoint: ${status} - ${message}`);
      }
    }
    
    // Test verify endpoint
    try {
      const verifyResponse = await axios.get(`${baseUrl}/api/system-health/cleanup/verify`);
      console.log('✅ Verify endpoint accessible:', verifyResponse.status);
    } catch (error) {
      const status = error.response?.status;
      const message = error.response?.data?.message || error.message;
      
      if (status === 500) {
        console.log(`❌ Verify endpoint: 500 Internal Server Error - ${message}`);
      } else if (status === 401 || status === 403) {
        console.log(`✅ Verify endpoint: ${status} (Auth required - endpoint exists)`);
      } else if (status === 404) {
        console.log(`❌ Verify endpoint: 404 Not Found - endpoint missing`);
      } else {
        console.log(`⚠️ Verify endpoint: ${status} - ${message}`);
      }
    }
    
    // Test task status endpoint
    try {
      const taskResponse = await axios.put(`${baseUrl}/api/tasks/1/status`, {
        status: 'completed'
      });
      console.log('✅ Task status endpoint accessible:', taskResponse.status);
    } catch (error) {
      const status = error.response?.status;
      const message = error.response?.data?.message || error.message;
      
      if (status === 500) {
        console.log(`❌ Task status endpoint: 500 Internal Server Error - ${message}`);
      } else if (status === 401 || status === 403) {
        console.log(`✅ Task status endpoint: ${status} (Auth required - endpoint exists)`);
      } else if (status === 404) {
        console.log(`❌ Task status endpoint: 404 Not Found - endpoint missing`);
      } else {
        console.log(`⚠️ Task status endpoint: ${status} - ${message}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Server not accessible:', error.message);
  }
}

testCleanupEndpoints();
