#!/usr/bin/env node

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER,
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
});

async function simpleTest() {
    console.log('🧪 Simple Shift Test...\n');
    
    try {
        // Test 1: Check if function exists
        const funcCheck = await pool.query(`
            SELECT COUNT(*) as count FROM pg_proc WHERE proname = 'evaluate_shift_status'
        `);
        console.log(`Functions found: ${funcCheck.rows[0].count}`);
        
        // Test 2: Try to call the function with a simple test
        const result = await pool.query(`
            SELECT evaluate_shift_status(1, CURRENT_TIMESTAMP) as test_result
        `);
        console.log(`Function call result: ${result.rows[0].test_result}`);
        
        // Test 3: Check current shifts
        const shifts = await pool.query(`
            SELECT id, truck_id, driver_id, shift_type, status, start_time, end_time 
            FROM driver_shifts 
            LIMIT 3
        `);
        console.log('\nCurrent shifts:');
        shifts.rows.forEach(shift => {
            console.log(`  ID: ${shift.id}, Truck: ${shift.truck_id}, Type: ${shift.shift_type}, Status: ${shift.status}`);
        });
        
        console.log('\n✅ Basic tests passed!');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await pool.end();
    }
}

if (require.main === module) {
    simpleTest();
}

module.exports = { simpleTest };