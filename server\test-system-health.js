/**
 * System Health API Test Script
 * 
 * This script tests the system health API endpoints to ensure they are working correctly.
 * It makes requests to the API and logs the responses.
 * 
 * Run with: node server/test-system-health.js
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');
require('dotenv').config();

// Create a test JWT token with admin privileges
const createTestToken = () => {
  const payload = {
    user: {
      id: 1,
      email: '<EMAIL>',
      is_admin: true
    }
  };
  
  return jwt.sign(payload, process.env.JWT_SECRET, { expiresIn: '1h' });
};

// Test the system health API endpoints
async function testSystemHealthAPI() {
  try {
    const token = createTestToken();
    const baseURL = process.env.ENABLE_HTTPS === 'true' 
      ? `https://localhost:${process.env.HTTPS_PORT || 5443}`
      : `http://localhost:${process.env.BACKEND_HTTP_PORT || 5000}`;
    
    console.log('🔍 System Health API Test');
    console.log('========================\n');
    console.log(`Base URL: ${baseURL}`);
    console.log(`JWT Token: ${token.substring(0, 20)}...\n`);
    
    // Create axios instance with token
    const api = axios.create({
      baseURL,
      headers: {
        Authorization: token,
        'Content-Type': 'application/json'
      },
      validateStatus: () => true // Don't throw on error status codes
    });
    
    // Test 1: Get system health status
    console.log('Test 1: GET /api/system-health/status');
    const statusResponse = await api.get('/api/system-health/status');
    console.log(`Status: ${statusResponse.status}`);
    
    if (statusResponse.status === 200) {
      const data = statusResponse.data;
      console.log('✅ Success! System health status retrieved');
      console.log(`Overall Status: ${data.overall.status}`);
      console.log(`Issues Count: ${data.overall.issues_count}`);
      console.log(`Timestamp: ${data.timestamp}`);
      
      console.log('\nModule Statuses:');
      console.log(`Shifts: ${data.shifts.status} (${data.shifts.issues.length} issues)`);
      console.log(`Assignments: ${data.assignments.status} (${data.assignments.issues.length} issues)`);
      console.log(`Trips: ${data.trips.status} (${data.trips.issues.length} issues)`);
      console.log(`Database: ${data.database.status}`);
      
      // Show some issues if they exist
      if (data.overall.issues_count > 0) {
        console.log('\nSample Issues:');
        
        if (data.shifts.issues.length > 0) {
          console.log(`- Shift Issue: ${data.shifts.issues[0].description}`);
        }
        
        if (data.assignments.issues.length > 0) {
          console.log(`- Assignment Issue: ${data.assignments.issues[0].description}`);
        }
        
        if (data.trips.issues.length > 0) {
          console.log(`- Trip Issue: ${data.trips.issues[0].description}`);
        }
      }
    } else {
      console.log('❌ Failed to retrieve system health status');
      console.log(`Error: ${statusResponse.data.error}`);
      console.log(`Message: ${statusResponse.data.message}`);
    }
    
    // Test 2: Test fix endpoints (these should return 501 Not Implemented for now)
    console.log('\nTest 2: POST /api/system-health/fix-shifts');
    const fixShiftsResponse = await api.post('/api/system-health/fix-shifts');
    console.log(`Status: ${fixShiftsResponse.status}`);
    console.log(`Message: ${fixShiftsResponse.data.message}`);
    
    console.log('\nTest 3: POST /api/system-health/fix-assignments');
    const fixAssignmentsResponse = await api.post('/api/system-health/fix-assignments');
    console.log(`Status: ${fixAssignmentsResponse.status}`);
    console.log(`Message: ${fixAssignmentsResponse.data.message}`);
    
    console.log('\nTest 4: POST /api/system-health/fix-trips');
    const fixTripsResponse = await api.post('/api/system-health/fix-trips');
    console.log(`Status: ${fixTripsResponse.status}`);
    console.log(`Message: ${fixTripsResponse.data.message}`);
    
    // Test 5: Test authentication requirement
    console.log('\nTest 5: GET /api/system-health/status (without auth token)');
    const noAuthApi = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json'
      },
      validateStatus: () => true
    });
    
    const noAuthResponse = await noAuthApi.get('/api/system-health/status');
    console.log(`Status: ${noAuthResponse.status}`);
    console.log(`Error: ${noAuthResponse.data.error}`);
    console.log(`Message: ${noAuthResponse.data.message}`);
    
    // Test 6: Test non-admin access
    console.log('\nTest 6: GET /api/system-health/status (non-admin user)');
    const nonAdminToken = jwt.sign(
      { user: { id: 2, email: '<EMAIL>', is_admin: false } },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );
    
    const nonAdminApi = axios.create({
      baseURL,
      headers: {
        Authorization: nonAdminToken,
        'Content-Type': 'application/json'
      },
      validateStatus: () => true
    });
    
    const nonAdminResponse = await nonAdminApi.get('/api/system-health/status');
    console.log(`Status: ${nonAdminResponse.status}`);
    console.log(`Error: ${nonAdminResponse.data.error}`);
    console.log(`Message: ${nonAdminResponse.data.message}`);
    
    console.log('\n✅ System Health API Test Complete');
    
  } catch (error) {
    console.error('❌ Test error:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testSystemHealthAPI();