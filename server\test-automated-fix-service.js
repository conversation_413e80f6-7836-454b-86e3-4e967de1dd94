/**
 * Automated Fix Service Test Script
 * 
 * This script tests the AutomatedFixService to ensure it is working correctly.
 * It calls the service methods and logs the responses.
 * 
 * Run with: node server/test-automated-fix-service.js
 */

import 'dotenv/config';
import AutomatedFixService from './services/AutomatedFixService.js';
import SystemMonitoringService from './services/SystemMonitoringService.js';

// Test the AutomatedFixService
async function testAutomatedFixService() {
  try {
    console.log('🔧 Automated Fix Service Test');
    console.log('===========================\n');
    
    const fixService = new AutomatedFixService();
    
    // Get initial system health status
    console.log('Getting initial system health status...');
    const initialHealth = await SystemMonitoringService.getSystemHealth();
    console.log(`Initial Overall Status: ${initialHealth.overall.status}`);
    console.log(`Initial Issues Count: ${initialHealth.overall.issues_count}`);
    
    console.log('\nModule Statuses Before Fixes:');
    console.log(`Shifts: ${initialHealth.shifts.status} (${initialHealth.shifts.issues.length} issues)`);
    console.log(`Assignments: ${initialHealth.assignments.status} (${initialHealth.assignments.issues.length} issues)`);
    console.log(`Trips: ${initialHealth.trips.status} (${initialHealth.trips.issues.length} issues)`);
    
    // Test 1: Fix shift management
    console.log('\nTest 1: fixShiftManagement()');
    console.log('---------------------------');
    try {
      if (initialHealth.shifts.issues.length > 0) {
        console.log(`Found ${initialHealth.shifts.issues.length} shift issues to fix`);
        const shiftFixResults = await fixService.fixShiftManagement();
        
        console.log('✅ Shift fix operation completed');
        console.log(`Success: ${shiftFixResults.success}`);
        console.log(`Message: ${shiftFixResults.message}`);
        console.log(`Timestamp: ${shiftFixResults.timestamp}`);
        
        console.log('\nFix Details:');
        console.log(`Before: ${shiftFixResults.details.before.issueCount} issues`);
        console.log(`After: ${shiftFixResults.details.after.issueCount} issues`);
        console.log(`Fixed: ${shiftFixResults.details.before.issueCount - shiftFixResults.details.after.issueCount} issues`);
        
        if (shiftFixResults.affectedRecords && shiftFixResults.affectedRecords.length > 0) {
          console.log(`Affected Records: ${shiftFixResults.affectedRecords.length}`);
        }
      } else {
        console.log('No shift issues to fix');
      }
    } catch (error) {
      console.log('❌ Failed to fix shift management issues');
      console.log(`Error: ${error.message}`);
    }
    
    // Test 2: Fix assignment management
    console.log('\nTest 2: fixAssignmentManagement()');
    console.log('-------------------------------');
    try {
      if (initialHealth.assignments.issues.length > 0) {
        console.log(`Found ${initialHealth.assignments.issues.length} assignment issues to fix`);
        const assignmentFixResults = await fixService.fixAssignmentManagement();
        
        console.log('✅ Assignment fix operation completed');
        console.log(`Success: ${assignmentFixResults.success}`);
        console.log(`Message: ${assignmentFixResults.message}`);
        console.log(`Timestamp: ${assignmentFixResults.timestamp}`);
        
        console.log('\nFix Details:');
        console.log(`Before: ${assignmentFixResults.details.before.noActiveShiftCount} issues`);
        console.log(`After: ${assignmentFixResults.details.after.noActiveShiftCount} issues`);
        console.log(`Fixed: ${assignmentFixResults.details.before.noActiveShiftCount - assignmentFixResults.details.after.noActiveShiftCount} issues`);
        
        if (assignmentFixResults.affectedRecords && assignmentFixResults.affectedRecords.length > 0) {
          console.log(`Affected Records: ${assignmentFixResults.affectedRecords.length}`);
        }
      } else {
        console.log('No assignment issues to fix');
      }
    } catch (error) {
      console.log('❌ Failed to fix assignment management issues');
      console.log(`Error: ${error.message}`);
    }
    
    // Test 3: Fix trip monitoring
    console.log('\nTest 3: fixTripMonitoring()');
    console.log('-------------------------');
    try {
      if (initialHealth.trips.issues.length > 0) {
        console.log(`Found ${initialHealth.trips.issues.length} trip issues to fix`);
        const tripFixResults = await fixService.fixTripMonitoring();
        
        console.log('✅ Trip fix operation completed');
        console.log(`Success: ${tripFixResults.success}`);
        console.log(`Message: ${tripFixResults.message}`);
        console.log(`Timestamp: ${tripFixResults.timestamp}`);
        
        if (tripFixResults.details && tripFixResults.details.fixedTrips) {
          console.log(`Fixed Trips: ${tripFixResults.details.fixedTrips.length}`);
          
          if (tripFixResults.details.fixedTrips.length > 0) {
            console.log('\nSample Fixed Trip:');
            const sampleTrip = tripFixResults.details.fixedTrips[0];
            console.log(`Trip ID: ${sampleTrip.trip_id}`);
            console.log(`Truck: ${sampleTrip.truck_number}`);
            console.log(`Previous Status: ${sampleTrip.previous_status}`);
            console.log(`New Status: ${sampleTrip.new_status}`);
          }
        }
        
        if (tripFixResults.affectedRecords && tripFixResults.affectedRecords.length > 0) {
          console.log(`Affected Records: ${tripFixResults.affectedRecords.length}`);
        }
      } else {
        console.log('No trip issues to fix');
      }
    } catch (error) {
      console.log('❌ Failed to fix trip monitoring issues');
      console.log(`Error: ${error.message}`);
    }
    
    // Get final system health status
    console.log('\nGetting final system health status...');
    const finalHealth = await SystemMonitoringService.getSystemHealth();
    console.log(`Final Overall Status: ${finalHealth.overall.status}`);
    console.log(`Final Issues Count: ${finalHealth.overall.issues_count}`);
    
    console.log('\nModule Statuses After Fixes:');
    console.log(`Shifts: ${finalHealth.shifts.status} (${finalHealth.shifts.issues.length} issues)`);
    console.log(`Assignments: ${finalHealth.assignments.status} (${finalHealth.assignments.issues.length} issues)`);
    console.log(`Trips: ${finalHealth.trips.status} (${finalHealth.trips.issues.length} issues)`);
    
    console.log('\n✅ Automated Fix Service Test Complete');
    
  } catch (error) {
    console.error('❌ Test error:', error.message);
  } finally {
    // Exit the process to close database connections
    process.exit(0);
  }
}

// Run the test
testAutomatedFixService();