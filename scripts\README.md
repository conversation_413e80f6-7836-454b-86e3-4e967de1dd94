# Shift Management Scripts

This directory contains automated tools for maintaining and monitoring the shift management system.

## 🧪 Testing Scripts

### `automated-shift-tests.js`
Comprehensive test suite for the shift management system.

**What it tests:**
- Database function signatures and functionality
- Day/night shift status logic
- Assignment display logic
- Overnight shift handling
- Auto-activation functionality

**Usage:**
```bash
node scripts/automated-shift-tests.js
```

**Expected Output:**
```
🚀 Starting Automated Shift Management Tests...
✅ PASSED: Database Functions
✅ PASSED: Shift Status Logic  
✅ PASSED: Assignment Display Logic
✅ PASSED: Overnight Shift Logic
✅ PASSED: Auto-Activation Function

🎉 ALL TESTS PASSED! Shift management system is working correctly.
```

## 📊 Monitoring Scripts

### `monitor-shift-status.js`
Real-time monitoring tool for shift statuses and system health.

**What it monitors:**
- Current time context and expected shift statuses
- Actual vs expected shift statuses
- Assignment display status
- Automatic issue detection and fixing

**Usage:**
```bash
node scripts/monitor-shift-status.js
```

**Expected Output:**
```
🔍 Shift Status Monitoring Tool
📅 Current Time Context: [time info]
📊 Current Shift Status: [status table]
📋 Assignment Display Status: [display table]
✅ All systems operational!
```

## 🔧 Maintenance Scripts

### `setup-cron-jobs.js`
Sets up automated maintenance tasks using cron jobs.

**What it creates:**
- Auto-activation script (runs every 15 minutes)
- Status monitoring (runs hourly)
- Automated tests (runs daily)
- Log directory and files

**Usage:**
```bash
node scripts/setup-cron-jobs.js
```

**Follow-up:** Manually install the generated cron jobs using the provided commands.

### `auto-activate-shifts.js` (Generated)
Automatically runs shift activation to keep statuses current.

**Usage:**
```bash
node scripts/auto-activate-shifts.js
```

**Note:** This script is created by `setup-cron-jobs.js` and designed for cron execution.

## 🔍 Diagnostic Scripts

### `check-schema.js`
Checks database schema and enum values for troubleshooting.

**Usage:**
```bash
node scripts/check-schema.js
```

### `check-trucks-schema.js`
Specifically checks the dump_trucks table schema.

**Usage:**
```bash
node scripts/check-trucks-schema.js
```

## 🚀 Quick Start

### Daily Health Check
```bash
# Run comprehensive tests
node scripts/automated-shift-tests.js

# Monitor current status
node scripts/monitor-shift-status.js
```

### Setup Automation
```bash
# Generate cron job configuration
node scripts/setup-cron-jobs.js

# Follow the instructions to install cron jobs
```

### Troubleshooting
```bash
# Check database schema
node scripts/check-schema.js

# Monitor and auto-fix issues
node scripts/monitor-shift-status.js
```

## 📋 Script Dependencies

All scripts require:
- Node.js
- PostgreSQL connection
- `.env` file with database credentials
- `pg` npm package

## 🔄 Maintenance Schedule

### Recommended Schedule
- **Real-time**: Automatic status transitions via database functions
- **Every 15 minutes**: Auto-activation (via cron)
- **Hourly**: Status monitoring (via cron)
- **Daily**: Full test suite (via cron)
- **Weekly**: Manual review of logs and system health

### Manual Checks
- Run tests after any system changes
- Monitor status during shift transition times (6 AM, 6 PM)
- Check logs for any error patterns
- Verify assignment display in the web interface

## 📞 Support

If any script fails:

1. **Check Prerequisites**: Ensure database is running and accessible
2. **Verify Environment**: Check `.env` file has correct database credentials
3. **Run Diagnostics**: Use `check-schema.js` to verify database structure
4. **Check Logs**: Review server logs for related errors
5. **Manual Fix**: Use the "Fix Assignment Display Issues" button in Settings

## 📈 Success Indicators

### Healthy System
- All automated tests pass (5/5)
- No status mismatches in monitoring
- Assignment display shows correct shift statuses
- No errors in script execution

### Warning Signs
- Test failures
- Status mismatches detected
- "No Active Shift" showing when shifts should be active
- Script execution errors

---

**Last Updated**: July 15, 2025
**System Status**: ✅ All scripts operational
**Test Results**: 5/5 passing