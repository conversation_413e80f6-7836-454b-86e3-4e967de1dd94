/**
 * Test database tables existence
 */

const { getClient } = require('./config/database');

async function testDatabaseTables() {
  let client;
  
  try {
    client = await getClient();
    
    console.log('Testing database tables...');
    
    // Check if system_tasks table exists
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('system_tasks', 'trip_logs', 'trips')
    `);
    
    console.log('Existing tables:', tablesResult.rows.map(r => r.table_name));
    
    // Check if system_tasks table exists specifically
    const systemTasksExists = tablesResult.rows.some(r => r.table_name === 'system_tasks');
    console.log('system_tasks table exists:', systemTasksExists);
    
    // Check if trip_logs table exists
    const tripLogsExists = tablesResult.rows.some(r => r.table_name === 'trip_logs');
    console.log('trip_logs table exists:', tripLogsExists);
    
    // Check if trips table exists
    const tripsExists = tablesResult.rows.some(r => r.table_name === 'trips');
    console.log('trips table exists:', tripsExists);
    
    if (systemTasksExists) {
      // Test creating a task
      const createResult = await client.query(`
        INSERT INTO system_tasks (type, priority, title, description)
        VALUES ('test', 'medium', 'Test Task', 'Test task for verification')
        RETURNING id
      `);
      console.log('✅ Successfully created test task with ID:', createResult.rows[0].id);
      
      // Test updating task status
      const updateResult = await client.query(`
        UPDATE system_tasks 
        SET status = 'completed' 
        WHERE id = $1 
        RETURNING *
      `, [createResult.rows[0].id]);
      console.log('✅ Successfully updated task status');
      
      // Clean up
      await client.query('DELETE FROM system_tasks WHERE id = $1', [createResult.rows[0].id]);
      console.log('✅ Cleaned up test task');
    }
    
  } catch (error) {
    console.error('❌ Database test error:', error.message);
  } finally {
    if (client) {
      client.release();
    }
  }
}

testDatabaseTables();
