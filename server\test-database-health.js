/**
 * Test script for database health monitoring functionality
 * 
 * This script tests the database health monitoring utilities to ensure they
 * correctly report database health metrics and identify potential issues.
 */

const {
  getDatabaseHealthMetrics,
  checkConnectionStatus,
  getPoolMetrics,
  getTableStatistics,
  getIndexUsageStatistics,
  getSlowQueryStatistics,
  getActiveConnections
} = require('./utils/database-health-monitor');

// Configure console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

/**
 * Run all database health tests
 */
async function runTests() {
  console.log(`${colors.cyan}=== Database Health Monitoring Tests ===${colors.reset}`);
  console.log(`${colors.blue}Starting tests at ${new Date().toISOString()}${colors.reset}`);
  
  try {
    // Test 1: Basic connection status
    console.log(`\n${colors.cyan}Test 1: Basic Connection Status${colors.reset}`);
    const connectionStatus = await checkConnectionStatus();
    console.log(`Connection status: ${getStatusColor(connectionStatus.status)}${connectionStatus.status}${colors.reset}`);
    console.log(`Database: ${connectionStatus.database}`);
    console.log(`Response time: ${connectionStatus.response_time_ms}ms`);
    
    // Test 2: Connection pool metrics
    console.log(`\n${colors.cyan}Test 2: Connection Pool Metrics${colors.reset}`);
    const poolMetrics = await getPoolMetrics();
    console.log(`Pool status: ${getStatusColor(poolMetrics.status)}${poolMetrics.status}${colors.reset}`);
    console.log(`Total connections: ${poolMetrics.stats.total}/${poolMetrics.stats.max} (${poolMetrics.utilization.total_percent}%)`);
    console.log(`Idle connections: ${poolMetrics.stats.idle} (${poolMetrics.utilization.idle_percent}%)`);
    console.log(`Active connections: ${poolMetrics.stats.total - poolMetrics.stats.idle} (${poolMetrics.utilization.active_percent}%)`);
    console.log(`Waiting clients: ${poolMetrics.stats.waiting}`);
    
    if (poolMetrics.issues.length > 0) {
      console.log(`\nIssues detected:`);
      poolMetrics.issues.forEach(issue => {
        console.log(`- ${getSeverityColor(issue.severity)}${issue.severity}${colors.reset}: ${issue.description}`);
      });
    }
    
    // Test 3: Table statistics
    console.log(`\n${colors.cyan}Test 3: Table Statistics${colors.reset}`);
    const tableStats = await getTableStatistics();
    console.log(`Table stats status: ${getStatusColor(tableStats.status)}${tableStats.status}${colors.reset}`);
    console.log(`Tables analyzed: ${tableStats.tables.length}`);
    
    // Show top 5 tables by row count
    console.log(`\nTop 5 tables by row count:`);
    tableStats.tables.slice(0, 5).forEach(table => {
      console.log(`- ${table.name}: ${table.row_count.toLocaleString()} rows, ${table.dead_ratio} dead tuples`);
    });
    
    if (tableStats.issues.length > 0) {
      console.log(`\nIssues detected:`);
      tableStats.issues.forEach(issue => {
        console.log(`- ${getSeverityColor(issue.severity)}${issue.severity}${colors.reset}: ${issue.description}`);
      });
    }
    
    // Test 4: Index usage statistics
    console.log(`\n${colors.cyan}Test 4: Index Usage Statistics${colors.reset}`);
    const indexUsage = await getIndexUsageStatistics();
    console.log(`Index usage status: ${getStatusColor(indexUsage.status)}${indexUsage.status}${colors.reset}`);
    console.log(`Indexes analyzed: ${indexUsage.indexes.length}`);
    console.log(`Unused indexes: ${indexUsage.unused_indexes.length}`);
    
    if (indexUsage.issues.length > 0) {
      console.log(`\nIssues detected:`);
      indexUsage.issues.forEach(issue => {
        console.log(`- ${getSeverityColor(issue.severity)}${issue.severity}${colors.reset}: ${issue.description}`);
      });
    }
    
    // Test 5: Slow query statistics
    console.log(`\n${colors.cyan}Test 5: Slow Query Statistics${colors.reset}`);
    const slowQueries = await getSlowQueryStatistics();
    console.log(`Slow query status: ${getStatusColor(slowQueries.status)}${slowQueries.status}${colors.reset}`);
    
    if (!slowQueries.available) {
      console.log(`pg_stat_statements extension not available: ${slowQueries.message}`);
    } else {
      console.log(`Slow queries detected: ${slowQueries.slow_queries.length}`);
      
      if (slowQueries.slow_queries.length > 0) {
        console.log(`\nTop 3 slowest queries:`);
        slowQueries.slow_queries.slice(0, 3).forEach(query => {
          console.log(`- ${query.mean_seconds}s avg (${query.calls} calls): ${query.query.substring(0, 80)}...`);
        });
      }
      
      if (slowQueries.issues.length > 0) {
        console.log(`\nIssues detected:`);
        slowQueries.issues.forEach(issue => {
          console.log(`- ${getSeverityColor(issue.severity)}${issue.severity}${colors.reset}: ${issue.description}`);
        });
      }
    }
    
    // Test 6: Active connections
    console.log(`\n${colors.cyan}Test 6: Active Connections${colors.reset}`);
    const activeConnections = await getActiveConnections();
    console.log(`Active connections status: ${getStatusColor(activeConnections.status)}${activeConnections.status}${colors.reset}`);
    console.log(`Active connections: ${activeConnections.connections.length}`);
    
    if (activeConnections.connections.length > 0) {
      console.log(`\nActive queries:`);
      activeConnections.connections.slice(0, 3).forEach(conn => {
        console.log(`- ${conn.username} (${conn.duration_seconds}s): ${conn.query.substring(0, 80)}...`);
      });
    }
    
    if (activeConnections.issues.length > 0) {
      console.log(`\nIssues detected:`);
      activeConnections.issues.forEach(issue => {
        console.log(`- ${getSeverityColor(issue.severity)}${issue.severity}${colors.reset}: ${issue.description}`);
      });
    }
    
    // Test 7: Complete health metrics
    console.log(`\n${colors.cyan}Test 7: Complete Health Metrics${colors.reset}`);
    const healthMetrics = await getDatabaseHealthMetrics();
    console.log(`Overall health status: ${getStatusColor(healthMetrics.status)}${healthMetrics.status}${colors.reset}`);
    console.log(`Response time: ${healthMetrics.response_time_ms}ms`);
    
    // Count total issues
    const totalIssues = [
      ...(healthMetrics.pool.issues || []),
      ...(healthMetrics.tables.issues || []),
      ...(healthMetrics.indexes.issues || []),
      ...(healthMetrics.queries.issues || []),
      ...(healthMetrics.active_connections.issues || [])
    ].length;
    
    console.log(`Total issues detected: ${totalIssues}`);
    
    console.log(`\n${colors.green}All tests completed successfully!${colors.reset}`);
    
  } catch (error) {
    console.error(`${colors.red}Error running tests:${colors.reset}`, error);
  }
}

/**
 * Get color code for status
 * @param {string} status - Status string
 * @returns {string} Color code
 */
function getStatusColor(status) {
  switch (status) {
    case 'operational':
      return colors.green;
    case 'warning':
      return colors.yellow;
    case 'critical':
      return colors.red;
    default:
      return colors.reset;
  }
}

/**
 * Get color code for severity
 * @param {string} severity - Severity string
 * @returns {string} Color code
 */
function getSeverityColor(severity) {
  switch (severity) {
    case 'low':
      return colors.blue;
    case 'medium':
      return colors.yellow;
    case 'high':
      return colors.red;
    default:
      return colors.reset;
  }
}

// Run the tests
runTests().catch(err => {
  console.error(`${colors.red}Fatal error:${colors.reset}`, err);
  process.exit(1);
});