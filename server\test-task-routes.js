/**
 * Test task routes specifically
 */

const axios = require('axios');

async function testTaskRoutes() {
  const baseUrl = 'http://localhost:5000';
  
  console.log('Testing task routes...');
  
  try {
    // Test GET /api/tasks
    try {
      const getResponse = await axios.get(`${baseUrl}/api/tasks`);
      console.log('✅ GET /api/tasks:', getResponse.status);
    } catch (error) {
      console.log('❌ GET /api/tasks:', error.response?.status, error.response?.data?.message);
    }
    
    // Test POST /api/tasks
    try {
      const postResponse = await axios.post(`${baseUrl}/api/tasks`, {
        type: 'test',
        priority: 'medium',
        title: 'Test Task',
        description: 'Test task'
      });
      console.log('✅ POST /api/tasks:', postResponse.status);
      
      const taskId = postResponse.data.data?.id;
      if (taskId) {
        // Test PUT /api/tasks/:id
        try {
          const putResponse = await axios.put(`${baseUrl}/api/tasks/${taskId}`, {
            status: 'in_progress'
          });
          console.log('✅ PUT /api/tasks/:id:', putResponse.status);
        } catch (error) {
          console.log('❌ PUT /api/tasks/:id:', error.response?.status, error.response?.data?.message);
        }
        
        // Test PUT /api/tasks/:id/status
        try {
          const statusResponse = await axios.put(`${baseUrl}/api/tasks/${taskId}/status`, {
            status: 'completed'
          });
          console.log('✅ PUT /api/tasks/:id/status:', statusResponse.status);
        } catch (error) {
          console.log('❌ PUT /api/tasks/:id/status:', error.response?.status, error.response?.data?.message);
        }
      }
    } catch (error) {
      console.log('❌ POST /api/tasks:', error.response?.status, error.response?.data?.message);
    }
    
  } catch (error) {
    console.error('❌ Server not accessible:', error.message);
  }
}

testTaskRoutes();
