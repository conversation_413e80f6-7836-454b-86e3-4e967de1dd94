#!/usr/bin/env node

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER,
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
});

async function testShiftLogic() {
    console.log('🧪 Testing Shift Status Logic...\n');
    
    try {
        // Test 1: Create a day shift
        console.log('📅 Testing Day Shift Logic:');
        const dayShiftResult = await pool.query(`
            WITH test_shift AS (
                INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
                VALUES (1, 1, 'day', CURRENT_DATE, CURRENT_DATE, '08:00:00', '16:00:00', 'scheduled', 'single')
                RETURNING id
            )
            SELECT 
                evaluate_shift_status(id, CURRENT_DATE + '07:00:00'::TIME) as before_start,
                evaluate_shift_status(id, CURRENT_DATE + '12:00:00'::TIME) as during_shift,
                evaluate_shift_status(id, CURRENT_DATE + '17:00:00'::TIME) as after_end,
                id
            FROM test_shift
        `);
        
        const dayShift = dayShiftResult.rows[0];
        console.log(`  Before start (7 AM): ${dayShift.before_start} ✓`);
        console.log(`  During shift (12 PM): ${dayShift.during_shift} ✓`);
        console.log(`  After end (5 PM): ${dayShift.after_end} ✓`);
        
        // Clean up
        await pool.query('DELETE FROM driver_shifts WHERE id = $1', [dayShift.id]);
        
        // Test 2: Create a night shift
        console.log('\n🌙 Testing Night Shift Logic:');
        const nightShiftResult = await pool.query(`
            WITH test_shift AS (
                INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
                VALUES (1, 1, 'night', CURRENT_DATE, CURRENT_DATE, '22:00:00', '06:00:00', 'scheduled', 'single')
                RETURNING id
            )
            SELECT 
                evaluate_shift_status(id, CURRENT_DATE + '20:00:00'::TIME) as before_start,
                evaluate_shift_status(id, CURRENT_DATE + '23:00:00'::TIME) as during_evening,
                evaluate_shift_status(id, (CURRENT_DATE + INTERVAL '1 day') + '03:00:00'::TIME) as during_morning,
                evaluate_shift_status(id, (CURRENT_DATE + INTERVAL '1 day') + '07:00:00'::TIME) as after_end,
                id
            FROM test_shift
        `);
        
        const nightShift = nightShiftResult.rows[0];
        console.log(`  Before start (8 PM): ${nightShift.before_start} ✓`);
        console.log(`  During evening (11 PM): ${nightShift.during_evening} ✓`);
        console.log(`  During morning (3 AM next day): ${nightShift.during_morning} ✓`);
        console.log(`  After end (7 AM next day): ${nightShift.after_end} ✓`);
        
        // Clean up
        await pool.query('DELETE FROM driver_shifts WHERE id = $1', [nightShift.id]);
        
        // Test 3: Auto-activation function
        console.log('\n⚡ Testing Auto-Activation Function:');
        await pool.query('SELECT schedule_auto_activation()');
        console.log('  Auto-activation function executed successfully ✓');
        
        console.log('\n✅ All tests passed! Shift logic is working correctly.');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    } finally {
        await pool.end();
    }
}

if (require.main === module) {
    testShiftLogic();
}

module.exports = { testShiftLogic };