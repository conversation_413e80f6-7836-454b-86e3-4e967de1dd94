#!/bin/bash

# ============================================================================
# HAULING QR TRIP SYSTEM - UBUNTU 24.04 AUTO DEPLOYMENT SCRIPT
# ============================================================================
# Modern Interactive Deployment with Cloudflare SSL Integration
# Version: 2.0
# Compatible: Ubuntu 24.04 LTS
# Author: Hauling QR System Team
# ============================================================================

set -euo pipefail

# ============================================================================
# COLORS AND STYLING
# ============================================================================
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly WHITE='\033[1;37m'
readonly BOLD='\033[1m'
readonly NC='\033[0m' # No Color

# ============================================================================
# CONFIGURATION VARIABLES
# ============================================================================
readonly SCRIPT_VERSION="2.0"
readonly SCRIPT_NAME="Hauling QR Auto-Deploy"
readonly LOG_FILE="/tmp/hauling-qr-deploy-$(date +%Y%m%d_%H%M%S).log"
readonly APP_NAME="hauling-qr-system"
readonly APP_DIR="/opt/$APP_NAME"
readonly APP_USER="hauling"
readonly NODE_VERSION="18"
readonly POSTGRES_VERSION="15"

# Default configuration
DOMAIN_NAME=""
CLOUDFLARE_SSL_MODE="flexible"
DB_PASSWORD=""
JWT_SECRET=""
ADMIN_EMAIL=""
ADMIN_PASSWORD=""
GIT_REPO_URL=""
ENABLE_MONITORING="true"
ENABLE_BACKUPS="true"
BACKUP_SCHEDULE="0 2 * * *"
ENVIRONMENT="production"# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "INFO")  echo -e "${CYAN}[INFO]${NC} $message" | tee -a "$LOG_FILE" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" | tee -a "$LOG_FILE" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $message" | tee -a "$LOG_FILE" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE" ;;
        "STEP") echo -e "\n${PURPLE}${BOLD}[STEP]${NC} $message" | tee -a "$LOG_FILE" ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Progress bar function
show_progress() {
    local current=$1
    local total=$2
    local width=50
    local percentage=$((current * 100 / total))
    local completed=$((current * width / total))
    local remaining=$((width - completed))
    
    printf "\r${CYAN}Progress: [${NC}"
    printf "%*s" $completed | tr ' ' '█'
    printf "%*s" $remaining | tr ' ' '░'
    printf "${CYAN}] %d%% (%d/%d)${NC}" $percentage $current $total
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log "ERROR" "This script should not be run as root for security reasons"
        log "INFO" "Please run as a regular user with sudo privileges"
        exit 1
    fi
}

# Check Ubuntu version
check_ubuntu_version() {
    if ! grep -q "Ubuntu 24.04" /etc/os-release; then
        log "WARNING" "This script is optimized for Ubuntu 24.04 LTS"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# Generate secure password
generate_password() {
    local length=${1:-32}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# Validate email format
validate_email() {
    local email="$1"
    if [[ $email =~ ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$ ]]; then
        return 0
    else
        return 1
    fi
}

# Validate domain format
validate_domain() {
    local domain="$1"
    if [[ $domain =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$ ]]; then
        return 0
    else
        return 1
    fi
}# ============================================================================
# INTERACTIVE CONFIGURATION
# ============================================================================

show_banner() {
    clear
    echo -e "${PURPLE}${BOLD}"
    cat << "EOF"
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║    🚛 HAULING QR TRIP MANAGEMENT SYSTEM - AUTO DEPLOYMENT 🚛                ║
║                                                                              ║
║    Modern Interactive Deployment for Ubuntu 24.04 LTS                       ║
║    with Cloudflare SSL Integration                                           ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
    echo -e "${CYAN}Version: $SCRIPT_VERSION${NC}"
    echo -e "${CYAN}Log file: $LOG_FILE${NC}"
    echo
}

collect_configuration() {
    log "STEP" "Configuration Setup"
    
    echo -e "${BOLD}Let's configure your Hauling QR Trip System deployment!${NC}"
    echo
    
    # Domain configuration
    while true; do
        read -p "🌐 Enter your domain name (e.g., hauling.example.com): " DOMAIN_NAME
        if validate_domain "$DOMAIN_NAME"; then
            break
        else
            echo -e "${RED}Invalid domain format. Please try again.${NC}"
        fi
    done    
    # Cloudflare SSL Mode
    echo
    echo -e "${BOLD}🔒 Cloudflare SSL Configuration:${NC}"
    echo "1) Flexible SSL (HTTP to server, HTTPS to client) - Easiest"
    echo "2) Full SSL (HTTPS to server with self-signed cert) - Recommended"
    echo "3) Full (Strict) SSL (HTTPS with valid cert) - Most Secure"
    echo
    while true; do
        read -p "Select SSL mode (1-3): " ssl_choice
        case $ssl_choice in
            1) CLOUDFLARE_SSL_MODE="flexible"; break ;;
            2) CLOUDFLARE_SSL_MODE="full"; break ;;
            3) CLOUDFLARE_SSL_MODE="full_strict"; break ;;
            *) echo -e "${RED}Invalid choice. Please select 1, 2, or 3.${NC}" ;;
        esac
    done
    
    # Database configuration
    echo
    echo -e "${BOLD}🗄️ Database Configuration:${NC}"
    while true; do
        read -s -p "Enter PostgreSQL password (leave empty for auto-generated): " DB_PASSWORD
        echo
        if [[ -z "$DB_PASSWORD" ]]; then
            DB_PASSWORD=$(generate_password 24)
            echo -e "${GREEN}Auto-generated secure database password${NC}"
            break
        elif [[ ${#DB_PASSWORD} -ge 12 ]]; then
            break
        else
            echo -e "${RED}Password must be at least 12 characters long${NC}"
        fi
    done
    
    # JWT Secret
    echo
    echo -e "${BOLD}🔐 Security Configuration:${NC}"
    JWT_SECRET=$(generate_password 64)
    echo -e "${GREEN}Generated secure JWT secret${NC}"    
    # Admin user configuration
    echo
    while true; do
        read -p "👤 Enter admin email: " ADMIN_EMAIL
        if validate_email "$ADMIN_EMAIL"; then
            break
        else
            echo -e "${RED}Invalid email format. Please try again.${NC}"
        fi
    done
    
    while true; do
        read -s -p "Enter admin password (min 8 chars): " ADMIN_PASSWORD
        echo
        if [[ ${#ADMIN_PASSWORD} -ge 8 ]]; then
            break
        else
            echo -e "${RED}Password must be at least 8 characters long${NC}"
        fi
    done
    
    # Git repository
    echo
    read -p "📦 Git repository URL (leave empty for manual upload): " GIT_REPO_URL
    
    # Optional features
    echo
    echo -e "${BOLD}🔧 Optional Features:${NC}"
    read -p "Enable system monitoring? (Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        ENABLE_MONITORING="false"
    fi
    
    read -p "Enable automatic backups? (Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        ENABLE_BACKUPS="false"
    fi    
    # Environment
    echo
    echo -e "${BOLD}🌍 Environment:${NC}"
    echo "1) Production (recommended)"
    echo "2) Staging"
    echo "3) Development"
    while true; do
        read -p "Select environment (1-3): " env_choice
        case $env_choice in
            1) ENVIRONMENT="production"; break ;;
            2) ENVIRONMENT="staging"; break ;;
            3) ENVIRONMENT="development"; break ;;
            *) echo -e "${RED}Invalid choice. Please select 1, 2, or 3.${NC}" ;;
        esac
    done
}

show_configuration_summary() {
    echo
    log "INFO" "Configuration Summary"
    echo -e "${BOLD}Please review your configuration:${NC}"
    echo
    echo -e "${CYAN}Domain:${NC} $DOMAIN_NAME"
    echo -e "${CYAN}SSL Mode:${NC} $CLOUDFLARE_SSL_MODE"
    echo -e "${CYAN}Database Password:${NC} [HIDDEN]"
    echo -e "${CYAN}Admin Email:${NC} $ADMIN_EMAIL"
    echo -e "${CYAN}Git Repository:${NC} ${GIT_REPO_URL:-'Manual upload'}"
    echo -e "${CYAN}Monitoring:${NC} $ENABLE_MONITORING"
    echo -e "${CYAN}Backups:${NC} $ENABLE_BACKUPS"
    echo -e "${CYAN}Environment:${NC} $ENVIRONMENT"
    echo
    
    read -p "Continue with deployment? (Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        log "INFO" "Deployment cancelled by user"
        exit 0
    fi
}# ============================================================================
# DEPLOYMENT STEPS
# ============================================================================

step_system_update() {
    log "STEP" "System Update and Package Installation"
    
    log "INFO" "Updating system packages..."
    sudo apt update && sudo apt upgrade -y
    
    log "INFO" "Installing essential packages..."
    sudo apt install -y \
        curl wget gnupg2 software-properties-common \
        apt-transport-https ca-certificates git build-essential \
        unzip ufw fail2ban htop tree jq openssl \
        nginx postgresql postgresql-contrib \
        certbot python3-certbot-nginx \
        redis-server supervisor
    
    log "SUCCESS" "System packages installed successfully"
}

step_nodejs_installation() {
    log "STEP" "Node.js Installation"
    
    if command_exists node; then
        local current_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [[ "$current_version" -ge "$NODE_VERSION" ]]; then
            log "INFO" "Node.js $current_version is already installed"
            return 0
        fi
    fi
    
    log "INFO" "Installing Node.js $NODE_VERSION..."
    curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | sudo -E bash -
    sudo apt install -y nodejs
    
    # Install PM2 globally
    sudo npm install -g pm2
    
    log "SUCCESS" "Node.js $(node --version) and PM2 installed successfully"
}step_user_creation() {
    log "STEP" "Application User Creation"
    
    if id "$APP_USER" &>/dev/null; then
        log "INFO" "User $APP_USER already exists"
    else
        log "INFO" "Creating application user: $APP_USER"
        sudo useradd -r -s /bin/bash -d "$APP_DIR" -m "$APP_USER"
        sudo usermod -aG sudo "$APP_USER"
        log "SUCCESS" "User $APP_USER created successfully"
    fi
    
    # Create application directory
    sudo mkdir -p "$APP_DIR"
    sudo chown -R "$APP_USER:$APP_USER" "$APP_DIR"
}

step_database_setup() {
    log "STEP" "PostgreSQL Database Setup"
    
    # Start and enable PostgreSQL
    sudo systemctl start postgresql
    sudo systemctl enable postgresql
    
    # Create database and user
    log "INFO" "Creating database and user..."
    sudo -u postgres psql << EOF
CREATE DATABASE hauling_qr_system;
CREATE USER hauling_user WITH ENCRYPTED PASSWORD '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON DATABASE hauling_qr_system TO hauling_user;
ALTER USER hauling_user CREATEDB;
\q
EOF
    
    log "SUCCESS" "Database setup completed"
}step_application_deployment() {
    log "STEP" "Application Deployment"
    
    if [[ -n "$GIT_REPO_URL" ]]; then
        log "INFO" "Cloning repository from $GIT_REPO_URL"
        sudo -u "$APP_USER" git clone "$GIT_REPO_URL" "$APP_DIR" || {
            log "ERROR" "Failed to clone repository"
            log "INFO" "Please upload your application files manually to $APP_DIR"
            read -p "Press Enter when files are uploaded..."
        }
    else
        log "INFO" "Please upload your application files to $APP_DIR"
        log "INFO" "Required structure:"
        echo "  $APP_DIR/"
        echo "  ├── server/"
        echo "  ├── client/"
        echo "  ├── database/"
        echo "  ├── package.json"
        echo "  └── .env"
        read -p "Press Enter when files are uploaded..."
    fi
    
    # Change to application directory
    cd "$APP_DIR"
    
    # Install dependencies
    log "INFO" "Installing application dependencies..."
    sudo -u "$APP_USER" npm install --production
    
    # Install server dependencies
    if [[ -d "server" ]]; then
        cd server
        sudo -u "$APP_USER" npm install --production
        cd ..
    fi
    
    # Install and build client
    if [[ -d "client" ]]; then
        cd client
        sudo -u "$APP_USER" npm install
        sudo -u "$APP_USER" npm run build
        cd ..
    fi
    
    log "SUCCESS" "Application dependencies installed"
}step_environment_configuration() {
    log "STEP" "Environment Configuration"
    
    # Create production environment file
    cat > "$APP_DIR/.env" << EOF
# ============================================================================
# HAULING QR TRIP SYSTEM - PRODUCTION CONFIGURATION
# ============================================================================

# Environment
NODE_ENV=$ENVIRONMENT

# Server Configuration
PORT=5000
HOST=0.0.0.0
BACKEND_HTTP_PORT=5000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=hauling_user
DB_PASSWORD=$DB_PASSWORD
DB_POOL_MAX=25
DB_POOL_MIN=5

# Security Configuration
JWT_SECRET=$JWT_SECRET
JWT_EXPIRY=24h

# CORS Configuration
CORS_ORIGIN=https://$DOMAIN_NAME
ALLOWED_ORIGINS=https://$DOMAIN_NAME,https://www.$DOMAIN_NAME

# Client Configuration
REACT_APP_API_URL=https://$DOMAIN_NAME/api
REACT_APP_WS_URL=wss://$DOMAIN_NAME
REACT_APP_USE_HTTPS=true
EOF    cat >> "$APP_DIR/.env" << EOF

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_FILE_PATH=$APP_DIR/logs/app.log

# QR Code Configuration
QR_CODE_SIZE=200
QR_CODE_QUALITY=H
REACT_APP_QR_CAMERA_FACING=environment
REACT_APP_QR_SCAN_DELAY=500

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=$APP_DIR/uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf
EOF
    
    # Set proper permissions
    sudo chown "$APP_USER:$APP_USER" "$APP_DIR/.env"
    sudo chmod 600 "$APP_DIR/.env"
    
    log "SUCCESS" "Environment configuration created"
}

step_database_initialization() {
    log "STEP" "Database Initialization"
    
    # Initialize database schema
    if [[ -f "$APP_DIR/database/init.sql" ]]; then
        log "INFO" "Initializing database schema..."
        PGPASSWORD="$DB_PASSWORD" psql -h localhost -U hauling_user -d hauling_qr_system -f "$APP_DIR/database/init.sql"
        log "SUCCESS" "Database schema initialized"
    else
        log "WARNING" "Database initialization file not found"
    fi
    
    # Run migrations if available
    if [[ -f "$APP_DIR/database/run-migration.js" ]]; then
        log "INFO" "Running database migrations..."
        cd "$APP_DIR"
        sudo -u "$APP_USER" node database/run-migration.js
        log "SUCCESS" "Database migrations completed"
    fi
}step_ssl_configuration() {
    log "STEP" "SSL Configuration for Cloudflare"
    
    case "$CLOUDFLARE_SSL_MODE" in
        "flexible")
            log "INFO" "Configuring for Cloudflare Flexible SSL (HTTP backend)"
            # No SSL certificates needed on server
            ;;
        "full")
            log "INFO" "Configuring for Cloudflare Full SSL (self-signed cert)"
            # Create self-signed certificate
            sudo mkdir -p /etc/nginx/ssl
            sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
                -keyout /etc/nginx/ssl/private.key \
                -out /etc/nginx/ssl/certificate.crt \
                -subj "/C=US/ST=State/L=City/O=Organization/CN=$DOMAIN_NAME"
            sudo chmod 600 /etc/nginx/ssl/private.key
            sudo chmod 644 /etc/nginx/ssl/certificate.crt
            ;;
        "full_strict")
            log "INFO" "Configuring for Cloudflare Full (Strict) SSL"
            # Use Let's Encrypt with DNS challenge
            log "WARNING" "Full (Strict) SSL requires valid certificates"
            log "INFO" "You may need to configure Cloudflare API for automatic cert management"
            ;;
    esac
    
    log "SUCCESS" "SSL configuration completed for $CLOUDFLARE_SSL_MODE mode"
}step_nginx_configuration() {
    log "STEP" "Nginx Configuration"
    
    # Create Nginx configuration
    cat > /tmp/hauling-qr-system << 'EOF'
# Cloudflare IP ranges for real IP detection
set_real_ip_from ************/22;
set_real_ip_from ************/22;
set_real_ip_from **********/22;
set_real_ip_from **********/13;
set_real_ip_from **********/14;
set_real_ip_from *************/18;
set_real_ip_from **********/22;
set_real_ip_from ************/18;
set_real_ip_from ***********/15;
set_real_ip_from **********/13;
set_real_ip_from ************/20;
set_real_ip_from ************/20;
set_real_ip_from ************/20;
set_real_ip_from *************/22;
set_real_ip_from ************/17;
set_real_ip_from 2400:cb00::/32;
set_real_ip_from 2606:4700::/32;
set_real_ip_from 2803:f800::/32;
set_real_ip_from 2405:b500::/32;
set_real_ip_from 2405:8100::/32;
set_real_ip_from 2c0f:f248::/32;
set_real_ip_from 2a06:98c0::/29;
real_ip_header CF-Connecting-IP;

server {
    listen 80;
EOF    # Add SSL configuration if needed
    if [[ "$CLOUDFLARE_SSL_MODE" == "full" ]]; then
        cat >> /tmp/hauling-qr-system << 'EOF'
    listen 443 ssl;
    
    # SSL Configuration
    ssl_certificate /etc/nginx/ssl/certificate.crt;
    ssl_certificate_key /etc/nginx/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
EOF
    fi

    # Add domain and remaining configuration
    cat >> /tmp/hauling-qr-system << EOF
    server_name $DOMAIN_NAME www.$DOMAIN_NAME;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https:; connect-src 'self' wss: https:;" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
EOF    # Continue Nginx configuration
    cat >> /tmp/hauling-qr-system << EOF

    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone \$binary_remote_addr zone=login:10m rate=5r/m;

    # Serve React build files
    location / {
        root $APP_DIR/client/build;
        index index.html index.htm;
        try_files \$uri \$uri/ /index.html;

        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API routes with rate limiting
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
EOF    # Complete Nginx configuration
    cat >> /tmp/hauling-qr-system << EOF

    # Login rate limiting
    location /api/auth/login {
        limit_req zone=login burst=5 nodelay;
        
        proxy_pass http://localhost:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # WebSocket support
    location /socket.io/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://localhost:5000/health;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # Upload endpoint with size limit
    location /api/upload {
        client_max_body_size 10M;
        proxy_pass http://localhost:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF    # Install Nginx configuration
    sudo mv /tmp/hauling-qr-system /etc/nginx/sites-available/
    sudo ln -sf /etc/nginx/sites-available/hauling-qr-system /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # Test and reload Nginx
    sudo nginx -t
    sudo systemctl reload nginx
    
    log "SUCCESS" "Nginx configuration completed"
}

step_pm2_configuration() {
    log "STEP" "PM2 Process Management Setup"
    
    # Create PM2 ecosystem file
    cat > "$APP_DIR/ecosystem.config.js" << EOF
module.exports = {
  apps: [{
    name: '$APP_NAME',
    script: 'server/server.js',
    cwd: '$APP_DIR',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: '$ENVIRONMENT',
      PORT: 5000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: '$APP_DIR/logs/err.log',
    out_file: '$APP_DIR/logs/out.log',
    log_file: '$APP_DIR/logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'client/build'],
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s'
  }]
};
EOF    # Create logs directory
    sudo mkdir -p "$APP_DIR/logs"
    sudo chown -R "$APP_USER:$APP_USER" "$APP_DIR"
    
    # Start application with PM2
    sudo -u "$APP_USER" bash -c "cd $APP_DIR && pm2 start ecosystem.config.js --env $ENVIRONMENT"
    sudo -u "$APP_USER" pm2 save
    
    # Setup PM2 startup
    sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u "$APP_USER" --hp "$APP_DIR"
    
    log "SUCCESS" "PM2 configuration completed"
}

step_firewall_configuration() {
    log "STEP" "Firewall Configuration"
    
    # Reset UFW
    sudo ufw --force reset
    
    # Set default policies
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    
    # Allow SSH
    sudo ufw allow ssh
    
    # Allow HTTP and HTTPS
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    
    # Enable firewall
    sudo ufw --force enable
    
    log "SUCCESS" "Firewall configured successfully"
}step_security_configuration() {
    log "STEP" "Security Configuration"
    
    # Configure Fail2Ban
    cat > /tmp/jail.local << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
maxretry = 10

[nginx-botsearch]
enabled = true
filter = nginx-botsearch
logpath = /var/log/nginx/access.log
maxretry = 2
EOF
    
    sudo mv /tmp/jail.local /etc/fail2ban/
    sudo systemctl restart fail2ban
    sudo systemctl enable fail2ban
    
    log "SUCCESS" "Security configuration completed"
}step_monitoring_setup() {
    if [[ "$ENABLE_MONITORING" == "true" ]]; then
        log "STEP" "Monitoring Setup"
        
        # Create scripts directory
        sudo mkdir -p "$APP_DIR/scripts"
        
        # Create monitoring script
        cat > "$APP_DIR/scripts/health-check.sh" << 'EOF'
#!/bin/bash

# Health check script for Hauling QR System
LOG_FILE="/var/log/hauling-qr-health.log"

check_service() {
    local service=$1
    if systemctl is-active --quiet $service; then
        echo "$(date): $service is running" >> $LOG_FILE
        return 0
    else
        echo "$(date): $service is NOT running" >> $LOG_FILE
        return 1
    fi
}

check_url() {
    local url=$1
    if curl -f -s $url > /dev/null; then
        echo "$(date): $url is accessible" >> $LOG_FILE
        return 0
    else
        echo "$(date): $url is NOT accessible" >> $LOG_FILE
        return 1
    fi
}

# Check services
check_service nginx
check_service postgresql
check_service redis-server

# Check PM2 application
if pm2 list | grep -q "hauling-qr-system.*online"; then
    echo "$(date): Application is running" >> $LOG_FILE
else
    echo "$(date): Application is NOT running" >> $LOG_FILE
    # Restart application
    pm2 restart hauling-qr-system
fi

# Check application endpoint
check_url "http://localhost:5000/health"

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): WARNING - Disk usage is ${DISK_USAGE}%" >> $LOG_FILE
fi

# Check memory usage
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.2f", $3*100/$2}')
if (( $(echo "$MEMORY_USAGE > 80" | bc -l) )); then
    echo "$(date): WARNING - Memory usage is ${MEMORY_USAGE}%" >> $LOG_FILE
fi
EOF
        
        sudo chmod +x "$APP_DIR/scripts/health-check.sh"
        sudo chown "$APP_USER:$APP_USER" "$APP_DIR/scripts/health-check.sh"
        
        # Add to crontab
        (crontab -l 2>/dev/null; echo "*/5 * * * * $APP_DIR/scripts/health-check.sh") | crontab -
        
        log "SUCCESS" "Monitoring setup completed"
    fi
}step_backup_setup() {
    if [[ "$ENABLE_BACKUPS" == "true" ]]; then
        log "STEP" "Backup Setup"
        
        # Create backup directory
        sudo mkdir -p "$APP_DIR/backups"
        sudo chown "$APP_USER:$APP_USER" "$APP_DIR/backups"
        
        # Create backup script
        cat > "$APP_DIR/scripts/backup-db.sh" << EOF
#!/bin/bash
BACKUP_DIR="$APP_DIR/backups"
DATE=\$(date +%Y%m%d_%H%M%S)
DB_NAME="hauling_qr_system"
DB_USER="hauling_user"

mkdir -p \$BACKUP_DIR

PGPASSWORD="$DB_PASSWORD" pg_dump -h localhost -U \$DB_USER \$DB_NAME > \$BACKUP_DIR/backup_\$DATE.sql

# Keep only last 7 days of backups
find \$BACKUP_DIR -name "backup_*.sql" -mtime +7 -delete

echo "\$(date): Database backup completed: backup_\$DATE.sql" >> /var/log/hauling-qr-backup.log
EOF
        
        sudo chmod +x "$APP_DIR/scripts/backup-db.sh"
        sudo chown "$APP_USER:$APP_USER" "$APP_DIR/scripts/backup-db.sh"
        
        # Add to crontab
        (sudo -u "$APP_USER" crontab -l 2>/dev/null; echo "$BACKUP_SCHEDULE $APP_DIR/scripts/backup-db.sh") | sudo -u "$APP_USER" crontab -
        
        log "SUCCESS" "Backup setup completed"
    fi
}step_log_rotation() {
    log "STEP" "Log Rotation Setup"
    
    # Create logrotate configuration
    cat > /tmp/hauling-qr-system << EOF
$APP_DIR/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 $APP_USER $APP_USER
    postrotate
        sudo -u $APP_USER pm2 reloadLogs
    endscript
}

/var/log/nginx/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data adm
    postrotate
        systemctl reload nginx
    endscript
}
EOF
    
    sudo mv /tmp/hauling-qr-system /etc/logrotate.d/
    
    log "SUCCESS" "Log rotation configured"
}# ============================================================================
# VERIFICATION AND TESTING
# ============================================================================

verify_deployment() {
    log "STEP" "Deployment Verification"
    
    local errors=0
    
    # Check services
    for service in nginx postgresql redis-server; do
        if systemctl is-active --quiet $service; then
            log "SUCCESS" "$service is running"
        else
            log "ERROR" "$service is not running"
            ((errors++))
        fi
    done
    
    # Check PM2 application
    if sudo -u "$APP_USER" pm2 list | grep -q "$APP_NAME.*online"; then
        log "SUCCESS" "Application is running via PM2"
    else
        log "ERROR" "Application is not running"
        ((errors++))
    fi
    
    # Check database connection
    if PGPASSWORD="$DB_PASSWORD" psql -h localhost -U hauling_user -d hauling_qr_system -c "SELECT 1;" >/dev/null 2>&1; then
        log "SUCCESS" "Database connection successful"
    else
        log "ERROR" "Database connection failed"
        ((errors++))
    fi
    
    # Check application endpoint
    sleep 5  # Give application time to start
    if curl -f -s "http://localhost:5000/health" >/dev/null; then
        log "SUCCESS" "Application health check passed"
    else
        log "ERROR" "Application health check failed"
        ((errors++))
    fi
    
    # Check Nginx configuration
    if sudo nginx -t >/dev/null 2>&1; then
        log "SUCCESS" "Nginx configuration is valid"
    else
        log "ERROR" "Nginx configuration has errors"
        ((errors++))
    fi
    
    return $errors
}create_admin_user() {
    log "STEP" "Creating Admin User"
    
    # Create admin user script
    cat > /tmp/create-admin.js << EOF
const bcrypt = require('bcryptjs');
const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'hauling_qr_system',
  user: 'hauling_user',
  password: '$DB_PASSWORD'
});

async function createAdmin() {
  try {
    const hashedPassword = await bcrypt.hash('$ADMIN_PASSWORD', 12);
    
    const result = await pool.query(
      'INSERT INTO users (username, email, password_hash, full_name, role, status) VALUES (\$1, \$2, \$3, \$4, \$5, \$6) ON CONFLICT (email) DO NOTHING RETURNING id',
      ['admin', '$ADMIN_EMAIL', hashedPassword, 'System Administrator', 'admin', 'active']
    );
    
    if (result.rows.length > 0) {
      console.log('Admin user created successfully');
    } else {
      console.log('Admin user already exists');
    }
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await pool.end();
  }
}

createAdmin();
EOF
    
    # Run admin creation script
    cd "$APP_DIR"
    sudo -u "$APP_USER" node /tmp/create-admin.js
    rm /tmp/create-admin.js
    
    log "SUCCESS" "Admin user setup completed"
}# ============================================================================
# MAIN DEPLOYMENT FUNCTION
# ============================================================================

main_deployment() {
    local total_steps=15
    local current_step=0
    
    log "INFO" "Starting deployment with $total_steps steps"
    
    # Step 1: System Update
    ((current_step++))
    show_progress $current_step $total_steps
    step_system_update
    
    # Step 2: Node.js Installation
    ((current_step++))
    show_progress $current_step $total_steps
    step_nodejs_installation
    
    # Step 3: User Creation
    ((current_step++))
    show_progress $current_step $total_steps
    step_user_creation
    
    # Step 4: Database Setup
    ((current_step++))
    show_progress $current_step $total_steps
    step_database_setup
    
    # Step 5: Application Deployment
    ((current_step++))
    show_progress $current_step $total_steps
    step_application_deployment
    
    # Step 6: Environment Configuration
    ((current_step++))
    show_progress $current_step $total_steps
    step_environment_configuration    
    # Step 7: Database Initialization
    ((current_step++))
    show_progress $current_step $total_steps
    step_database_initialization
    
    # Step 8: SSL Configuration
    ((current_step++))
    show_progress $current_step $total_steps
    step_ssl_configuration
    
    # Step 9: Nginx Configuration
    ((current_step++))
    show_progress $current_step $total_steps
    step_nginx_configuration
    
    # Step 10: PM2 Configuration
    ((current_step++))
    show_progress $current_step $total_steps
    step_pm2_configuration
    
    # Step 11: Firewall Configuration
    ((current_step++))
    show_progress $current_step $total_steps
    step_firewall_configuration
    
    # Step 12: Security Configuration
    ((current_step++))
    show_progress $current_step $total_steps
    step_security_configuration
    
    # Step 13: Monitoring Setup
    ((current_step++))
    show_progress $current_step $total_steps
    step_monitoring_setup
    
    # Step 14: Backup Setup
    ((current_step++))
    show_progress $current_step $total_steps
    step_backup_setup
    
    # Step 15: Log Rotation
    ((current_step++))
    show_progress $current_step $total_steps
    step_log_rotation
    
    echo # New line after progress bar    
    # Create admin user
    create_admin_user
    
    # Verify deployment
    if verify_deployment; then
        log "SUCCESS" "Deployment completed successfully!"
        show_deployment_summary
    else
        log "ERROR" "Deployment verification failed"
        show_troubleshooting_guide
        exit 1
    fi
}

# ============================================================================
# SUMMARY AND TROUBLESHOOTING
# ============================================================================

show_deployment_summary() {
    echo
    echo -e "${GREEN}${BOLD}"
    cat << "EOF"
╔══════════════════════════════════════════════════════════════════════════════╗
║                    DEPLOYMENT COMPLETED SUCCESSFULLY!                       ║
╚══════════════════════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
    
    echo -e "${CYAN}🎉 Hauling QR Trip System is now running!${NC}"
    echo
    
    echo -e "${BOLD}📋 System Information:${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${CYAN}🌐 Application URL:${NC} https://$DOMAIN_NAME"
    echo -e "${CYAN}🔐 Admin Email:${NC} $ADMIN_EMAIL"
    echo -e "${CYAN}🔒 SSL Mode:${NC} $CLOUDFLARE_SSL_MODE"
    echo -e "${CYAN}🗄️ Database:${NC} PostgreSQL (hauling_qr_system)"
    echo -e "${CYAN}🚀 Process Manager:${NC} PM2"
    echo -e "${CYAN}🌍 Environment:${NC} $ENVIRONMENT"
    echo -e "${CYAN}📊 Monitoring:${NC} $ENABLE_MONITORING"
    echo -e "${CYAN}💾 Backups:${NC} $ENABLE_BACKUPS"
    echo    
    echo -e "${BOLD}🔧 Service Status:${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    systemctl is-active --quiet nginx && echo -e "${GREEN}✅ Nginx: Running${NC}" || echo -e "${RED}❌ Nginx: Stopped${NC}"
    systemctl is-active --quiet postgresql && echo -e "${GREEN}✅ PostgreSQL: Running${NC}" || echo -e "${RED}❌ PostgreSQL: Stopped${NC}"
    sudo -u "$APP_USER" pm2 list | grep -q "$APP_NAME.*online" && echo -e "${GREEN}✅ Application: Running${NC}" || echo -e "${RED}❌ Application: Stopped${NC}"
    systemctl is-active --quiet fail2ban && echo -e "${GREEN}✅ Fail2Ban: Running${NC}" || echo -e "${RED}❌ Fail2Ban: Stopped${NC}"
    echo
    
    echo -e "${BOLD}📚 Important Next Steps:${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "1. 🔐 Configure Cloudflare SSL settings:"
    echo "   • Go to SSL/TLS → Overview"
    echo "   • Set encryption mode to: $CLOUDFLARE_SSL_MODE"
    echo "   • Enable 'Always Use HTTPS'"
    echo
    echo "2. 🌐 Update DNS settings:"
    echo "   • Point $DOMAIN_NAME to your server IP"
    echo "   • Enable Cloudflare proxy (orange cloud)"
    echo
    echo "3. 🔒 Security recommendations:"
    echo "   • Change default admin password after first login"
    echo "   • Review firewall rules: sudo ufw status"
    echo "   • Monitor fail2ban: sudo fail2ban-client status"
    echo
    echo "4. 📊 Monitoring and maintenance:"
    echo "   • Check application logs: sudo -u $APP_USER pm2 logs $APP_NAME"
    echo "   • Monitor system: htop"
    echo "   • Health check: $APP_DIR/scripts/health-check.sh"
    echo    
    echo -e "${BOLD}📞 Support Information:${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${CYAN}📋 Deployment log:${NC} $LOG_FILE"
    echo -e "${CYAN}🔧 Application directory:${NC} $APP_DIR"
    echo -e "${CYAN}⚙️ Configuration file:${NC} $APP_DIR/.env"
    echo -e "${CYAN}📊 PM2 status:${NC} sudo -u $APP_USER pm2 status"
    echo
}

show_troubleshooting_guide() {
    echo
    echo -e "${YELLOW}${BOLD}🔧 TROUBLESHOOTING GUIDE${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo
    echo -e "${BOLD}Common Issues and Solutions:${NC}"
    echo
    echo -e "${CYAN}1. Application not starting:${NC}"
    echo "   • Check logs: sudo -u $APP_USER pm2 logs $APP_NAME"
    echo "   • Restart app: sudo -u $APP_USER pm2 restart $APP_NAME"
    echo "   • Check environment: cat $APP_DIR/.env"
    echo
    echo -e "${CYAN}2. Database connection issues:${NC}"
    echo "   • Test connection: PGPASSWORD='$DB_PASSWORD' psql -h localhost -U hauling_user -d hauling_qr_system"
    echo "   • Check PostgreSQL: sudo systemctl status postgresql"
    echo "   • Restart PostgreSQL: sudo systemctl restart postgresql"
    echo
    echo -e "${CYAN}3. Nginx/SSL issues:${NC}"
    echo "   • Test config: sudo nginx -t"
    echo "   • Check logs: sudo tail -f /var/log/nginx/error.log"
    echo "   • Restart Nginx: sudo systemctl restart nginx"
    echo
    echo -e "${CYAN}4. Cloudflare SSL issues:${NC}"
    echo "   • Verify SSL mode matches server configuration"
    echo "   • Check DNS propagation: nslookup $DOMAIN_NAME"
    echo "   • Verify Cloudflare proxy is enabled"
    echo    
    echo -e "${BOLD}Useful Commands:${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "• Check all services: sudo systemctl status nginx postgresql"
    echo "• View application status: sudo -u $APP_USER pm2 status"
    echo "• Monitor logs: sudo -u $APP_USER pm2 logs $APP_NAME --lines 50"
    echo "• Test API: curl -k http://localhost:5000/health"
    echo "• Check firewall: sudo ufw status"
    echo "• Monitor system: htop"
    echo
}

# ============================================================================
# SCRIPT ENTRY POINT
# ============================================================================

main() {
    # Initial checks
    check_root
    check_ubuntu_version
    
    # Show banner and collect configuration
    show_banner
    collect_configuration
    show_configuration_summary
    
    # Start deployment
    log "INFO" "Starting Hauling QR Trip System deployment"
    log "INFO" "Target domain: $DOMAIN_NAME"
    log "INFO" "SSL mode: $CLOUDFLARE_SSL_MODE"
    log "INFO" "Environment: $ENVIRONMENT"
    
    # Run main deployment
    main_deployment
    
    log "SUCCESS" "Deployment script completed successfully!"
}

# Handle script interruption
trap 'log "ERROR" "Deployment interrupted by user"; exit 1' INT TERM

# Run main function
main "$@"