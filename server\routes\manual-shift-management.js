/**
 * Manual Shift Management Routes
 * Purpose: API endpoints for manual shift status control
 */

const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const admin = require('../middleware/admin');
const manualShiftManagementService = require('../services/ManualShiftManagementService');

/**
 * @route   GET /api/manual-shift-management/active
 * @desc    Get all active shifts
 * @access  Private (Admin)
 */
router.get('/active', async (req, res) => {
  try {
    const activeShifts = await manualShiftManagementService.getActiveShifts();
    
    res.json({
      success: true,
      data: activeShifts
    });
  } catch (error) {
    console.error('Error getting active shifts:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while getting active shifts',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/manual-shift-management/scheduled
 * @desc    Get all scheduled shifts
 * @access  Private (Admin)
 */
router.get('/scheduled', async (req, res) => {
  try {
    const scheduledShifts = await manualShiftManagementService.getScheduledShifts();
    
    res.json({
      success: true,
      data: scheduledShifts
    });
  } catch (error) {
    console.error('Error getting scheduled shifts:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while getting scheduled shifts',
      error: error.message
    });
  }
});

/**
 * @route   POST /api/manual-shift-management/complete/:id
 * @desc    Manually complete a shift
 * @access  Private (Admin)
 */
router.post('/complete/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { notes } = req.body;
    // Use a default user ID for testing
    const userId = 1;
    
    const result = await manualShiftManagementService.completeShift(
      parseInt(id),
      userId,
      notes || ''
    );
    
    res.json({
      success: true,
      message: 'Shift completed successfully',
      data: result
    });
  } catch (error) {
    console.error('Error completing shift:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while completing shift',
      error: error.message
    });
  }
});

/**
 * @route   POST /api/manual-shift-management/cancel/:id
 * @desc    Manually cancel a shift
 * @access  Private (Admin)
 */
router.post('/cancel/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    // Use a default user ID for testing
    const userId = 1;
    
    const result = await manualShiftManagementService.cancelShift(
      parseInt(id),
      userId,
      reason || ''
    );
    
    res.json({
      success: true,
      message: 'Shift cancelled successfully',
      data: result
    });
  } catch (error) {
    console.error('Error cancelling shift:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while cancelling shift',
      error: error.message
    });
  }
});

/**
 * @route   POST /api/manual-shift-management/refresh
 * @desc    Force refresh shift statuses
 * @access  Private (Admin)
 */
router.post('/refresh', async (req, res) => {
  try {
    // Use a default user ID for testing
    const userId = 1;
    
    const result = await manualShiftManagementService.forceRefreshStatuses(userId);
    
    res.json({
      success: true,
      message: 'Shift statuses refreshed successfully',
      data: result
    });
  } catch (error) {
    console.error('Error refreshing shift statuses:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while refreshing shift statuses',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/manual-shift-management/summary
 * @desc    Get shift status summary
 * @access  Private (Admin)
 */
router.get('/summary', async (req, res) => {
  try {
    const summary = await manualShiftManagementService.getShiftStatusSummary();
    
    res.json({
      success: true,
      data: summary
    });
  } catch (error) {
    console.error('Error getting shift status summary:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while getting shift status summary',
      error: error.message
    });
  }
});

module.exports = router;