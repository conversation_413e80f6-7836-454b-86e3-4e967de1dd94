/**
 * Simple endpoint test without authentication
 */

const axios = require('axios');

async function testEndpoints() {
  const baseUrl = 'http://localhost:5000';
  
  console.log('Testing server connectivity...');
  
  try {
    // Test basic health endpoint
    const healthResponse = await axios.get(`${baseUrl}/health`);
    console.log('✅ Server is running:', healthResponse.data);
    
    // Test system health status (might work without auth for testing)
    try {
      const systemHealthResponse = await axios.get(`${baseUrl}/api/system-health/status`);
      console.log('✅ System health endpoint accessible');
    } catch (error) {
      console.log('⚠️ System health endpoint requires auth:', error.response?.status);
    }
    
    // Test fix endpoints (should return 401 or proper error, not 500)
    const fixEndpoints = [
      '/api/system-health/fix-shifts',
      '/api/system-health/fix-assignments', 
      '/api/system-health/fix-trips'
    ];
    
    for (const endpoint of fixEndpoints) {
      try {
        const response = await axios.post(`${baseUrl}${endpoint}`);
        console.log(`✅ ${endpoint}: ${response.status}`);
      } catch (error) {
        const status = error.response?.status;
        const message = error.response?.data?.message || error.message;
        
        if (status === 500) {
          console.log(`❌ ${endpoint}: 500 Internal Server Error - ${message}`);
        } else if (status === 401 || status === 403) {
          console.log(`✅ ${endpoint}: ${status} (Auth required - endpoint exists)`);
        } else {
          console.log(`⚠️ ${endpoint}: ${status} - ${message}`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Server not accessible:', error.message);
  }
}

testEndpoints();
