#!/usr/bin/env node

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER,
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
});

async function manualTest() {
    console.log('🔧 Manual Database Fix...\n');
    
    try {
        // Step 1: Find all function versions
        const functions = await pool.query(`
            SELECT proname, pronargs, oid 
            FROM pg_proc 
            WHERE proname IN ('evaluate_shift_status', 'schedule_auto_activation')
            ORDER BY proname, pronargs
        `);
        
        console.log('Found functions:');
        functions.rows.forEach(f => {
            console.log(`  ${f.proname}(${f.pronargs} args) - OID: ${f.oid}`);
        });
        
        // Step 2: Drop by OID to be specific
        for (const func of functions.rows) {
            try {
                await pool.query(`DROP FUNCTION ${func.oid}`);
                console.log(`✅ Dropped function OID ${func.oid}`);
            } catch (e) {
                console.log(`⚠️  Could not drop OID ${func.oid}: ${e.message}`);
            }
        }
        
        // Step 3: Create the working version
        console.log('\n3. Creating working functions...');
        
        await pool.query(`
            CREATE FUNCTION evaluate_shift_status(p_shift_id INTEGER, p_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
            RETURNS TEXT AS $$
            DECLARE
                shift_rec RECORD;
                curr_date DATE := p_timestamp::DATE;
                curr_time TIME := p_timestamp::TIME;
                is_overnight BOOLEAN;
                in_date_range BOOLEAN;
                in_time_window BOOLEAN;
                is_completed BOOLEAN;
            BEGIN
                SELECT start_date, end_date, start_time, end_time, status
                INTO shift_rec FROM driver_shifts WHERE id = p_shift_id;
                
                IF NOT FOUND THEN RETURN 'error'; END IF;
                IF shift_rec.status IN ('completed', 'cancelled') THEN RETURN shift_rec.status; END IF;
                
                is_overnight := shift_rec.end_time < shift_rec.start_time;
                in_date_range := curr_date BETWEEN shift_rec.start_date AND shift_rec.end_date;
                
                IF is_overnight THEN
                    in_time_window := (curr_time >= shift_rec.start_time OR curr_time <= shift_rec.end_time);
                    is_completed := (curr_date > shift_rec.end_date) OR 
                                   (curr_date = shift_rec.end_date AND curr_time > shift_rec.end_time);
                ELSE
                    in_time_window := (curr_time BETWEEN shift_rec.start_time AND shift_rec.end_time);
                    is_completed := (curr_date > shift_rec.end_date) OR 
                                   (curr_date = shift_rec.end_date AND curr_time > shift_rec.end_time);
                END IF;
                
                IF is_completed THEN RETURN 'completed';
                ELSIF in_date_range AND in_time_window THEN RETURN 'active';
                ELSE RETURN 'scheduled';
                END IF;
            END;
            $$ LANGUAGE plpgsql;
        `);
        
        console.log('✅ Created evaluate_shift_status function');
        
        await pool.query(`
            CREATE FUNCTION schedule_auto_activation()
            RETURNS void AS $$
            DECLARE
                rec RECORD;
                new_status TEXT;
                count_activated INTEGER := 0;
                count_completed INTEGER := 0;
            BEGIN
                FOR rec IN SELECT id, status FROM driver_shifts WHERE status != 'cancelled'
                LOOP
                    new_status := evaluate_shift_status(rec.id, CURRENT_TIMESTAMP);
                    IF new_status != rec.status AND new_status != 'error' THEN
                        UPDATE driver_shifts SET status = new_status::shift_status, updated_at = CURRENT_TIMESTAMP WHERE id = rec.id;
                        IF rec.status = 'scheduled' AND new_status = 'active' THEN count_activated := count_activated + 1; END IF;
                        IF rec.status = 'active' AND new_status = 'completed' THEN count_completed := count_completed + 1; END IF;
                    END IF;
                END LOOP;
                RAISE NOTICE 'Updated: % activated, % completed', count_activated, count_completed;
            END;
            $$ LANGUAGE plpgsql;
        `);
        
        console.log('✅ Created schedule_auto_activation function');
        
        // Step 4: Quick test
        console.log('\n4. Testing...');
        const testResult = await pool.query(`SELECT evaluate_shift_status(1, CURRENT_TIMESTAMP) as result`);
        console.log(`Test result: ${testResult.rows[0].result}`);
        
        console.log('\n🎉 SUCCESS! Functions are now working correctly!');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await pool.end();
    }
}

if (require.main === module) {
    manualTest();
}

module.exports = { manualTest };