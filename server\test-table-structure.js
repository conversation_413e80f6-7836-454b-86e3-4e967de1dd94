/**
 * Test system_tasks table structure
 */

const { getClient } = require('./config/database');

async function testTableStructure() {
  let client;
  
  try {
    client = await getClient();
    
    console.log('Checking system_tasks table structure...');
    
    // Get table columns
    const columnsResult = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'system_tasks'
      ORDER BY ordinal_position
    `);
    
    console.log('system_tasks columns:');
    columnsResult.rows.forEach(col => {
      console.log(`  ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    
    // Check if updated_at column exists
    const hasUpdatedAt = columnsResult.rows.some(col => col.column_name === 'updated_at');
    console.log('\nhas updated_at column:', hasUpdatedAt);
    
    if (!hasUpdatedAt) {
      console.log('Adding updated_at column...');
      await client.query(`
        ALTER TABLE system_tasks 
        ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      `);
      console.log('✅ Added updated_at column');
    }
    
  } catch (error) {
    console.error('❌ Table structure test error:', error.message);
  } finally {
    if (client) {
      client.release();
    }
  }
}

testTableStructure();
