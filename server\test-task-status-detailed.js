/**
 * Detailed test for task status endpoint
 */

const axios = require('axios');

async function testTaskStatusDetailed() {
  const baseUrl = 'http://localhost:5000';
  
  console.log('Testing task status endpoint in detail...');
  
  try {
    // First create a task
    console.log('1. Creating a test task...');
    const createResponse = await axios.post(`${baseUrl}/api/tasks`, {
      type: 'test',
      priority: 'medium',
      title: 'Test Task for Status Update',
      description: 'Test task for status endpoint verification'
    });
    
    console.log('✅ Task created:', createResponse.status, createResponse.data);
    
    const taskId = createResponse.data.data?.id;
    if (!taskId) {
      console.error('❌ No task ID returned');
      return;
    }
    
    console.log(`2. Testing status update for task ID: ${taskId}`);
    
    // Test the status endpoint with detailed error logging
    try {
      const statusResponse = await axios.put(`${baseUrl}/api/tasks/${taskId}/status`, {
        status: 'completed'
      });
      console.log('✅ Status update successful:', statusResponse.status, statusResponse.data);
    } catch (error) {
      console.log('❌ Status update failed:');
      console.log('  Status:', error.response?.status);
      console.log('  Data:', error.response?.data);
      console.log('  Headers:', error.response?.headers);
      
      // Try with different status values
      console.log('3. Trying with different status values...');
      
      const testStatuses = ['pending', 'in_progress', 'failed', 'cancelled'];
      for (const testStatus of testStatuses) {
        try {
          const testResponse = await axios.put(`${baseUrl}/api/tasks/${taskId}/status`, {
            status: testStatus
          });
          console.log(`✅ Status '${testStatus}' worked:`, testResponse.status);
          break;
        } catch (testError) {
          console.log(`❌ Status '${testStatus}' failed:`, testError.response?.status, testError.response?.data?.message);
        }
      }
    }
    
    // Clean up - delete the test task
    console.log('4. Cleaning up test task...');
    try {
      await axios.delete(`${baseUrl}/api/tasks/${taskId}`);
      console.log('✅ Test task cleaned up');
    } catch (cleanupError) {
      console.log('⚠️ Cleanup failed (task may not have delete endpoint)');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testTaskStatusDetailed();
