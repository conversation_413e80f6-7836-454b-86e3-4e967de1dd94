# Development Setup Guide

This guide provides detailed instructions for setting up the Hauling QR Trip Management System development environment.

## 🛠️ Prerequisites

### Required Software

1. **Node.js** (v16 or higher)
   - Download from [nodejs.org](https://nodejs.org/)
   - Verify installation: `node --version`

2. **PostgreSQL** (v12 or higher)
   - Download from [postgresql.org](https://www.postgresql.org/download/)
   - Verify installation: `psql --version`

3. **Git**
   - Download from [git-scm.com](https://git-scm.com/)
   - Verify installation: `git --version`

### Recommended Tools

- **VS Code** with extensions:
  - ES7+ React/Redux/React-Native snippets
  - Prettier - Code formatter
  - ESLint
  - PostgreSQL extension
- **Postman** or **Insomnia** for API testing
- **pgAdmin** for database management

## 🚀 Initial Setup

### 1. Clone and Install

```bash
# Clone the repository
git clone <repository-url>
cd hauling-qr-trip-system

# Install root dependencies
npm install

# Install client dependencies
cd client
npm install

# Install server dependencies
cd ../server
npm install

# Return to root
cd ..
```

### 2. Database Setup

#### Create Database and User

```sql
-- Connect to PostgreSQL as superuser
psql -U postgres

-- Create database
CREATE DATABASE hauling_qr_system;

-- Create user
CREATE USER hauling_user WITH ENCRYPTED PASSWORD 'your_secure_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE hauling_qr_system TO hauling_user;

-- Exit psql
\q
```

#### Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit the .env file with your settings
```

**Sample .env configuration:**

```env
# Environment
NODE_ENV=development
AUTO_DETECT_IP=true
ENABLE_HTTPS=false

# Server Configuration
HOST=0.0.0.0
PORT=5000
BACKEND_HTTP_PORT=5000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=hauling_user
DB_PASSWORD=your_secure_password

# Security
JWT_SECRET=your_very_secure_jwt_secret_key_here
JWT_EXPIRY=24h

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=secure_admin_password

# Client Configuration
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_WS_URL=ws://localhost:5000
REACT_APP_DOMAIN_NAME=localhost

# Features
ENABLE_MONITORING=true
ENABLE_BACKUPS=true
LOG_LEVEL=info

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# QR Code Configuration
QR_CODE_SIZE=200
QR_CODE_QUALITY=H
REACT_APP_QR_CAMERA_FACING=environment

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# CORS
CORS_ORIGIN=http://localhost:3000,https://localhost:3000
ALLOWED_ORIGINS=http://localhost:3000,https://localhost:3000

# Logging
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs/app.log

# Monitoring
SCAN_DELAY=5
REACT_APP_QR_SCAN_DELAY=5
```

#### Run Database Migrations

```bash
# Run migrations to create tables
npm run db:migrate
```

### 3. SSL Setup (Optional for HTTPS)

If you want to enable HTTPS for development:

```bash
# Create SSL directory
mkdir -p server/ssl/dev

# Generate self-signed certificates
cd server/ssl/dev
openssl req -x509 -newkey rsa:2048 -keyout private.key -out certificate.crt -days 365 -nodes -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"

# Set proper permissions
chmod 600 private.key
chmod 644 certificate.crt

# Return to root
cd ../../..

# Update .env to enable HTTPS
# Set ENABLE_HTTPS=true in .env
```

## 🏃‍♂️ Running the Application

### Development Mode

#### Option 1: Start Everything Together
```bash
# Start both client and server in development mode
npm run dev
```

#### Option 2: Start Components Separately

**Terminal 1 - Backend Server:**
```bash
cd server
npm run dev
# Server will start on http://localhost:5000
```

**Terminal 2 - Frontend Client:**
```bash
cd client
npm start
# Client will start on http://localhost:3000
```

**Terminal 3 - Database (if needed):**
```bash
# Start PostgreSQL service (varies by OS)
# Windows: net start postgresql-x64-13
# macOS: brew services start postgresql
# Linux: sudo systemctl start postgresql
```

### Production Mode

```bash
# Build client for production
cd client
npm run build

# Start production server
cd ..
npm start
```

## 🧪 Testing Setup

### Run Tests

```bash
# Run all tests
npm test

# Run server tests with coverage
cd server
npm run test:coverage

# Run client tests
cd client
npm test

# Run specific test files
npm test -- --testNamePattern="Assignment"
```

### Comprehensive System Testing

The system includes a comprehensive test script that validates the entire shift management system:

```bash
# Run complete system test
node scripts/test-complete-system.js
```

**What the comprehensive test validates:**
- ✅ Database function integrity (schedule_auto_activation)
- ✅ Current shift status display across all trucks and drivers
- ✅ Assignment display logic for Trip Monitoring interface
- ✅ Time context validation (day/night shift activation logic)
- ✅ Function signature verification (prevents server errors)
- ✅ Shift status statistics and counts

**Sample Test Output:**
```
🎯 Testing Complete Shift Management System...

1. Testing database functions...
✅ schedule_auto_activation() function works

2. Current Shift Status in Database:
   Truck   | Driver        | Type  | Status    | Time Range | Date Range
   --------|---------------|-------|-----------|------------|------------
   T001    | John Smith    | day   | active    | 06:00-18:00| 01-15 to 01-15
   T002    | Jane Doe      | night | scheduled | 18:00-06:00| 01-15 to 01-16

3. Testing Assignment Display Logic (Trip Monitoring):
   Assignment | Truck   | Assigned Driver | Active Shift Status
   -----------|---------|-----------------|--------------------
          1   | T001    | John Smith      | ✅ day Shift Active
          2   | T002    | Jane Doe        | 📅 night Shift Scheduled

4. Current Time Context:
   Date: 2025-01-15
   Time: 14:30:45 (Hour: 14)
   Expected: Night shifts (18:00-06:00) should be SCHEDULED
   Expected: Day shifts (06:00-18:00) should be ACTIVE

5. Testing function signatures...
✅ Function signature tests passed
   Total shifts: 4
   Active: 2
   Scheduled: 2
   Completed: 0

🎉 SYSTEM TEST COMPLETED!

📝 Summary:
   ✅ Database functions working correctly
   ✅ Shift statuses displaying properly
   ✅ Assignment display logic working
   ✅ Server endpoint updated to use corrected functions
   ✅ No more function signature errors

🚀 Next Steps:
   1. The "Fix Assignment Display Issues" button should now work
   2. Trip Monitoring should show correct shift statuses
   3. Shift Management data table should display correct statuses
   4. Server logs should no longer show function errors
```

**When to run this test:**
- After database migrations
- When troubleshooting shift display issues
- Before deploying changes to shift management
- When validating system integrity after updates

### Test Database Setup

For running tests, you may want a separate test database:

```sql
-- Create test database
CREATE DATABASE hauling_qr_system_test;
GRANT ALL PRIVILEGES ON DATABASE hauling_qr_system_test TO hauling_user;
```

Update your test configuration to use the test database.

## 🔧 Development Tools Configuration

### VS Code Settings

Create `.vscode/settings.json`:

```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "emmet.includeLanguages": {
    "javascript": "javascriptreact"
  },
  "files.associations": {
    "*.js": "javascriptreact"
  }
}
```

### ESLint Configuration

The project includes ESLint configuration. To run linting:

```bash
# Lint client code
cd client
npm run lint

# Lint server code
cd server
npm run lint
```

### Prettier Configuration

Format code with Prettier:

```bash
# Format client code
cd client
npm run format

# Format server code
cd server
npm run format
```

## 📊 Database Management

### Useful PostgreSQL Commands

```bash
# Connect to database
psql -h localhost -U hauling_user -d hauling_qr_system

# List tables
\dt

# Describe table structure
\d table_name

# View recent migrations
SELECT * FROM schema_migrations ORDER BY version DESC LIMIT 5;

# Check database size
SELECT pg_size_pretty(pg_database_size('hauling_qr_system'));
```

### Creating New Migrations

```bash
# Create new migration file
touch database/migrations/$(date +%Y%m%d%H%M%S)_your_migration_name.sql

# Add your SQL commands to the file
# Run migration
npm run db:migrate
```

## 🐛 Debugging

### Server Debugging

1. **Enable Debug Logs:**
   ```env
   LOG_LEVEL=debug
   ```

2. **Use Node.js Debugger:**
   ```bash
   cd server
   node --inspect server.js
   ```

3. **VS Code Debugging:**
   Create `.vscode/launch.json`:
   ```json
   {
     "version": "0.2.0",
     "configurations": [
       {
         "name": "Debug Server",
         "type": "node",
         "request": "launch",
         "program": "${workspaceFolder}/server/server.js",
         "env": {
           "NODE_ENV": "development"
         },
         "console": "integratedTerminal"
       }
     ]
   }
   ```

### Client Debugging

1. **React Developer Tools** browser extension
2. **Console Logging:**
   ```javascript
   console.log('Debug info:', data);
   ```

3. **Network Tab** in browser dev tools for API calls

## 🔍 Common Development Issues

### Port Already in Use
```bash
# Find process using port 5000
lsof -ti:5000

# Kill process
kill -9 <PID>

# Or use different port in .env
PORT=5001
```

### Database Connection Issues
1. Verify PostgreSQL is running
2. Check credentials in `.env`
3. Ensure database exists
4. Check firewall settings

### Module Not Found Errors
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

### CORS Issues
1. Check `CORS_ORIGIN` in `.env`
2. Verify client URL matches allowed origins
3. Check browser console for CORS errors

## 📱 Mobile Development Testing

### Testing on Mobile Devices

1. **Find your local IP:**
   ```bash
   # Windows
   ipconfig
   
   # macOS/Linux
   ifconfig
   ```

2. **Update .env for mobile access:**
   ```env
   AUTO_DETECT_IP=true
   REACT_APP_API_URL=http://YOUR_IP:5000/api
   REACT_APP_WS_URL=ws://YOUR_IP:5000
   ```

3. **Access from mobile:**
   - Open `http://YOUR_IP:3000` on mobile browser
   - Ensure mobile and computer are on same network

### QR Code Testing

1. **Camera Permissions:** Ensure browser has camera access
2. **HTTPS Requirement:** Some browsers require HTTPS for camera
3. **Test QR Codes:** Use online QR generators for testing
4. **Lighting Conditions:** Test in various lighting scenarios

## 🚀 Deployment Preparation

### Build for Production

```bash
# Build client
cd client
npm run build

# Test production build locally
cd ..
npm start
```

### Environment Variables for Production

Update `.env` for production:
```env
NODE_ENV=production
ENABLE_HTTPS=true
DB_HOST=your_production_db_host
REACT_APP_API_URL=https://yourdomain.com/api
REACT_APP_WS_URL=wss://yourdomain.com
```

## 📞 Getting Help

### Resources

- **Documentation:** Check `/docs` folder
- **Issues:** Review existing GitHub issues
- **Logs:** Check application logs in `/logs` directory
- **Database:** Use pgAdmin or psql for database inspection

### Debug Commands

```bash
# Check system status
npm run system:check

# Clear caches
npm run fix:shift-cache

# Test database connection
npm run db:test

# Verify environment configuration
npm run config:verify
```

---

This setup guide should get you up and running with the Hauling QR Trip Management System. If you encounter any issues not covered here, please check the main documentation or create an issue in the project repository.