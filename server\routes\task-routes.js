/**
 * Task Management API Routes
 * 
 * Provides endpoints for managing system tasks and recommendations
 */

const express = require('express');
const router = express.Router();
const { getClient } = require('../config/database');

/**
 * @route   GET /api/tasks
 * @desc    Get all tasks
 * @access  Public (for testing)
 */
router.get('/', async (req, res) => {
  try {
    const client = await getClient();
    
    const result = await client.query(`
      SELECT 
        id, 
        type, 
        priority, 
        status, 
        title, 
        description, 
        created_at, 
        scheduled_for, 
        completed_at
      FROM 
        system_tasks
      ORDER BY 
        CASE 
          WHEN priority = 'critical' THEN 1
          WHEN priority = 'high' THEN 2
          WHEN priority = 'medium' THEN 3
          WHEN priority = 'low' THEN 4
        END,
        created_at DESC
    `);
    
    client.release();
    
    res.json({
      success: true,
      data: result.rows
    });
  } catch (error) {
    console.error('Error getting tasks:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get tasks',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/tasks/recommendations
 * @desc    Get system recommendations
 * @access  Public (for testing)
 */
router.get('/recommendations', async (req, res) => {
  try {
    // For testing, return some sample recommendations
    const recommendations = [
      {
        id: 'rec-1',
        type: 'maintenance',
        priority: 'medium',
        title: 'Run database cleanup',
        description: 'Database has grown significantly. Consider running cleanup operations to optimize performance.',
        estimated_duration: 300, // 5 minutes
        auto_executable: true
      },
      {
        id: 'rec-2',
        type: 'monitoring',
        priority: 'high',
        title: 'Fix shift status inconsistencies',
        description: 'Several shifts have incorrect statuses based on their date/time ranges.',
        estimated_duration: 60, // 1 minute
        auto_executable: true
      },
      {
        id: 'rec-3',
        type: 'optimization',
        priority: 'low',
        title: 'Optimize trip queries',
        description: 'Trip queries are taking longer than expected. Consider adding indexes to improve performance.',
        estimated_duration: 600, // 10 minutes
        auto_executable: false
      }
    ];
    
    res.json({
      success: true,
      data: recommendations
    });
  } catch (error) {
    console.error('Error getting recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get recommendations',
      error: error.message
    });
  }
});

/**
 * @route   POST /api/tasks
 * @desc    Create a new task
 * @access  Public (for testing)
 */
router.post('/', async (req, res) => {
  try {
    const { type, priority, title, description, scheduled_for } = req.body;
    
    // Validate required fields
    if (!type || !priority || !title) {
      return res.status(400).json({
        success: false,
        message: 'Type, priority, and title are required'
      });
    }
    
    const client = await getClient();
    
    const result = await client.query(`
      INSERT INTO system_tasks (
        type, 
        priority, 
        status, 
        title, 
        description, 
        scheduled_for
      ) VALUES (
        $1, $2, 'pending', $3, $4, $5
      ) RETURNING *
    `, [type, priority, title, description, scheduled_for]);
    
    client.release();
    
    res.status(201).json({
      success: true,
      data: result.rows[0]
    });
  } catch (error) {
    console.error('Error creating task:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create task',
      error: error.message
    });
  }
});

/**
 * @route   PUT /api/tasks/:id
 * @desc    Update a task
 * @access  Public (for testing)
 */
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { status, priority, description, scheduled_for } = req.body;
    
    const client = await getClient();
    
    // Check if task exists
    const taskCheck = await client.query('SELECT id FROM system_tasks WHERE id = $1', [id]);
    
    if (taskCheck.rows.length === 0) {
      client.release();
      return res.status(404).json({
        success: false,
        message: `Task with ID ${id} not found`
      });
    }
    
    // Update task
    const result = await client.query(`
      UPDATE system_tasks
      SET 
        status = COALESCE($1, status),
        priority = COALESCE($2, priority),
        description = COALESCE($3, description),
        scheduled_for = COALESCE($4, scheduled_for),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $5
      RETURNING *
    `, [status, priority, description, scheduled_for, id]);
    
    client.release();
    
    res.json({
      success: true,
      data: result.rows[0]
    });
  } catch (error) {
    console.error('Error updating task:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update task',
      error: error.message
    });
  }
});

/**
 * @route   DELETE /api/tasks/:id
 * @desc    Delete a task
 * @access  Public (for testing)
 */
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const client = await getClient();
    
    // Check if task exists
    const taskCheck = await client.query('SELECT id FROM system_tasks WHERE id = $1', [id]);
    
    if (taskCheck.rows.length === 0) {
      client.release();
      return res.status(404).json({
        success: false,
        message: `Task with ID ${id} not found`
      });
    }
    
    // Delete task
    await client.query('DELETE FROM system_tasks WHERE id = $1', [id]);
    
    client.release();
    
    res.json({
      success: true,
      message: `Task with ID ${id} deleted successfully`
    });
  } catch (error) {
    console.error('Error deleting task:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete task',
      error: error.message
    });
  }
});

module.exports = router;