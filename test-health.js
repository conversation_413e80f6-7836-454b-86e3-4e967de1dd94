/**
 * Simple test script for system health monitoring
 */

console.log('Starting system health test...');

try {
  const SystemMonitoringService = require('./server/services/SystemMonitoringService');
  
  console.log('SystemMonitoringService loaded successfully');
  
  SystemMonitoringService.getSystemHealth()
    .then(result => {
      console.log('✅ System health test successful!');
      console.log('Result:', JSON.stringify(result, null, 2));
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ System health test failed:', error);
      console.error('Stack:', error.stack);
      process.exit(1);
    });
    
} catch (error) {
  console.error('❌ Failed to load SystemMonitoringService:', error);
  console.error('Stack:', error.stack);
  process.exit(1);
}