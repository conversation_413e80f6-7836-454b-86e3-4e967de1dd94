# Hauling QR Trip Management System

A comprehensive logistics management system for tracking hauling truck trips using QR code technology. The system enables real-time monitoring of truck movements between locations, driver assignments, and trip completion workflows.

## 🚛 Overview

The Hauling QR Trip Management System eliminates manual trip logging and provides real-time visibility into fleet operations. Trucks scan QR codes at locations to log arrivals/departures, enabling automated workflow management and comprehensive analytics.

### Key Features

- **QR Code-based Trip Tracking**: Trucks scan QR codes at locations to log arrivals/departures
- **Multi-Location Workflows**: Support for A→B→C extensions, C→B→C cycles, and dynamic route discovery
- **Real-time Dashboard**: Live monitoring of active trips, truck status, and performance metrics
- **Driver Management**: Assignment tracking, shift management, and performance analytics
- **Exception Handling**: Automated detection and management of workflow deviations
- **Mobile-First Design**: Responsive web application optimized for mobile devices and tablets
- **Customizable Appearance**: Logo customization, font settings, and visual theme configuration
- **System Health Monitoring**: Comprehensive monitoring and automated fixing for Shift Management, Assignment Management, and Trip Monitoring modules

## 🏗️ Architecture

Full-stack web application with separate client and server components, unified configuration system, and PostgreSQL database.

```
hauling-qr-trip-system/
├── client/                 # React frontend application
├── server/                 # Express.js backend API
├── database/              # Database schema and migrations
├── scripts/               # System startup and utility scripts
├── docs/                  # Project documentation
├── utils/                 # Shared utilities
├── tests/                 # Integration tests
├── .env                   # Unified environment configuration
└── package.json           # Root package with system-level scripts
```

## 🛠️ Tech Stack

### Frontend (Client)
- **Framework**: React 18.2.0 with React Router DOM
- **Styling**: Tailwind CSS 3.3.6 with custom design system
- **QR Code**: Multiple libraries (@yudiel/react-qr-scanner, html5-qrcode, qrcode.react)
- **Charts**: Chart.js with react-chartjs-2 for analytics
- **HTTP Client**: Axios for API communication
- **Build Tool**: Create React App (CRA)

### Backend (Server)
- **Runtime**: Node.js with Express.js framework
- **Database**: PostgreSQL with pg driver and connection pooling
- **Authentication**: JWT with bcryptjs for password hashing
- **Security**: Helmet, CORS, express-rate-limit
- **WebSocket**: ws library for real-time communication
- **QR Processing**: qrcode generation, jsqr for reading

### Database
- **Primary**: PostgreSQL with advanced features (JSONB, GIN indexes, materialized views)
- **Migration System**: Custom Node.js migration runner with 41+ migrations
- **Performance**: Connection pooling, optimized indexes
- **Advanced Functions**: Automated shift status evaluation and management

## 🚀 Quick Start

### Prerequisites

- Node.js (v16 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd hauling-qr-trip-system
   ```

2. **Install dependencies**
   ```bash
   # Install root dependencies
   npm install
   
   # Install client dependencies
   cd client && npm install
   
   # Install server dependencies
   cd ../server && npm install
   ```

3. **Configure environment**
   ```bash
   # Copy and configure environment file
   cp .env.example .env
   # Edit .env with your database credentials and settings
   ```

4. **Setup database**
   ```bash
   # Run database migrations
   npm run db:migrate
   ```

5. **Start the application**
   ```bash
   # Development mode (starts both client and server)
   npm run dev
   
   # Or start individually:
   # Frontend dev server (port 3000)
   cd client && npm start
   
   # Backend server (port 5000)
   cd server && npm run dev
   ```

## 📋 Available Scripts

### Root Level Scripts
- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm test` - Run test suite
- `npm run db:migrate` - Run database migrations

### Client Scripts
- `cd client && npm start` - Start React development server
- `cd client && npm run build` - Build for production
- `cd client && npm test` - Run client tests

### Server Scripts
- `cd server && npm run dev` - Start server with nodemon
- `cd server && npm test` - Run server tests
- `cd server && npm run test:coverage` - Run tests with coverage

## 🎨 Appearance Customization

The system includes comprehensive appearance customization options accessible through the Settings panel.

### Customizable Elements

#### Logo Settings
- **Custom Logo URL**: Upload and display your organization's logo
- **Logo Dimensions**: Adjustable width and height (20-200px)
- **Alt Text**: Customizable accessibility text
- **Live Preview**: Real-time preview of logo changes

#### Typography Settings
- **Font Families**: Choose from 8 professional font options including Inter, Arial, Georgia, and more
- **Font Sizes**: Customizable sizes for headers (18-32px), content (12-22px), and footer (10-18px)
- **Font Weights**: Light, Normal, Medium, Semi Bold, and Bold options
- **Separate Controls**: Independent settings for headers, content, and footer text

#### Visual Features
- **Preview Mode**: Test changes before applying them permanently
- **Live Updates**: See changes applied in real-time with CSS custom properties
- **Persistent Storage**: Settings saved to localStorage and applied across sessions
- **Reset Option**: One-click reset to default appearance settings

### CSS Custom Properties

The appearance system uses CSS custom properties for dynamic theming:

```css
/* Font Properties */
--font-header-family: 'Inter, system-ui, sans-serif'
--font-header-size: '24px'
--font-header-weight: '600'
--font-content-family: 'Inter, system-ui, sans-serif'
--font-content-size: '16px'
--font-content-weight: '400'
--font-footer-family: 'Inter, system-ui, sans-serif'
--font-footer-size: '14px'
--font-footer-weight: '400'

/* Logo Properties */
--logo-width: '40px'
--logo-height: '40px'
```

### Accessing Appearance Settings

1. Navigate to **Settings** from the main menu
2. Click on **🎨 Appearance Settings**
3. Customize logo, fonts, and visual elements
4. Use **Preview Mode** to test changes
5. Click **Save Settings** to apply permanently

## 🏥 System Health Monitoring

The System Health Monitoring feature provides comprehensive real-time monitoring and automated fixing capabilities for four core modules: Shift Management, Assignment Management, Trip Monitoring, and Database Health.

### Key Features

#### Centralized Health Dashboard
- **Real-time Status Indicators**: ✅ Operational, ⚠️ Issues Detected, ❌ Critical
- **Module-specific Monitoring**: Individual health checks for Shifts, Assignments, Trips, and Database
- **Automated Issue Detection**: Proactive identification of system inconsistencies
- **One-click Fixes**: Automated resolution of common issues
- **Mobile-First Design**: Fully responsive interface optimized for mobile devices and tablets
- **Accessibility Compliant**: WCAG AA compliant with comprehensive screen reader support

#### Shift Management Integration
- **Real-time Shift Verification**: Monitors day shifts (6 AM-6 PM) and night shifts (6 PM-6 AM)
- **Automatic Status Correction**: Executes `schedule_auto_activation()` database function
- **Cross-midnight Handling**: Correctly processes overnight shift transitions
- **Status Synchronization**: Ensures shift displays match actual status

#### Assignment Management Integration
- **Display Issue Detection**: Identifies "⚠️ No Active Shift" errors during active hours
- **Automatic Synchronization**: Aligns assignment displays with active shifts
- **Trip Monitoring Integration**: Updates truck assignment displays in real-time
- **Overnight Shift Support**: Handles "✅ night Shift Active" status correctly

#### Trip Monitoring Integration
- **Workflow Integrity Checks**: Verifies PENDING → IN_PROGRESS → COMPLETED → VERIFIED transitions
- **Driver Status Verification**: Validates driver assignments for dump trucks
- **Real-time Updates**: Automatic refresh of trip monitoring data
- **Critical Issue Alerts**: Immediate notifications for workflow problems

### Accessing System Health Monitor

1. Navigate to **Settings** from the main menu
2. Click on **🏥 System Health Monitor**
3. View real-time status for all modules
4. Click **Fix Issues** buttons to resolve problems automatically
5. Monitor task management and cleanup operations

### Automated Maintenance Features

#### Task Management System
- **Pending Task Tracking**: Monitors maintenance tasks with priority levels
- **Automated Scheduling**: Schedules cleanup and optimization tasks
- **Trend Analysis**: Provides system health recommendations
- **Task History**: Complete audit trail of maintenance activities

#### Code Cleanup Automation
- **Unused Function Detection**: Scans `server/**/*.js` and `scripts/**/*.js` files
- **Safety Preservation**: Protects critical functions (routes, middleware, database operations)
- **Backup and Rollback**: Automatic backup creation with rollback capabilities
- **Impact Analysis**: Detailed reports of cleanup operations

### API Endpoints

The System Health Monitor integrates with dedicated API endpoints:

- `GET /api/system-health/status` - Current health status for all modules
- `POST /api/system-health/fix-shifts` - Execute automated shift fixes
- `POST /api/system-health/fix-assignments` - Synchronize assignment displays
- `POST /api/system-health/fix-trips` - Resolve trip workflow issues
- `GET /api/tasks` - Task management and recommendations
- `POST /api/cleanup/analyze` - Code cleanup analysis
- `POST /api/cleanup/execute` - Execute cleanup operations

### Monitoring and Alerting

- **Automated Health Checks**: Run every 15 minutes during business hours
- **Critical Issue Alerts**: Immediate notifications for system problems
- **Trend Analysis**: Early warning for performance degradation
- **Escalation Management**: Manual intervention for failed automated fixes

## 🤖 AI Integration (MCP)

The system includes comprehensive Model Context Protocol (MCP) integration for enhanced development capabilities:

### Configured MCP Servers

- **Fetch MCP** - Web content fetching and processing
- **Memory Server** - Knowledge graph and persistent memory management  
- **Filesystem Server** - Advanced file system operations
- **Sequential Thinking** - Structured note-taking and thought organization
- **Brave Search** - Web search and information retrieval
- **Context7** - Library documentation and API references
- **PostgreSQL** - Direct database interaction and queries
- **Desktop Commander** - System-level operations and automation

### Key Benefits

- **Automated Code Generation** - AI-powered code scaffolding and templates
- **Intelligent Documentation** - Automated docs generation and maintenance
- **Database Analysis** - AI-assisted query optimization and schema analysis
- **Knowledge Management** - Persistent project context and relationships
- **Web Integration** - Seamless external data fetching and processing

For detailed MCP configuration and usage, see [MCP Integration Guide](docs/MCP_INTEGRATION_GUIDE.md).

## 🔧 Configuration

### Environment Variables

The system uses a unified `.env` file for configuration:

```env
# Environment
NODE_ENV=development
AUTO_DETECT_IP=true
ENABLE_HTTPS=false

# Server Configuration
HOST=0.0.0.0
PORT=5000
BACKEND_HTTP_PORT=5000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=hauling_user
DB_PASSWORD=your_password

# Security
JWT_SECRET=your_jwt_secret
JWT_EXPIRY=24h

# Client Configuration
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_WS_URL=ws://localhost:5000

# Features
ENABLE_MONITORING=true
ENABLE_BACKUPS=true
```

### Port Configuration
- **Frontend**: Always port 3000 (HTTP/HTTPS)
- **Backend**: Always port 5000 (HTTP/HTTPS)
- **Database**: PostgreSQL on port 5432

## 📊 Core Workflows

### Standard Trip Workflow
1. **Assignment Creation**: Dispatcher assigns truck to route
2. **Loading Start**: Driver scans QR at pickup location
3. **Loading End**: Driver confirms loading completion
4. **Unloading Start**: Driver scans QR at destination
5. **Unloading End**: Driver confirms delivery completion
6. **Trip Completed**: System marks trip as completed

### Multi-Location Workflows

#### A→B→C Extensions
- After completing A→B, truck continues to Point C
- Automatic assignment creation for new route
- Baseline trip marked as "Auto Completed"

#### C→B→C Cycles
- Continuous cycles: load at Point C, unload at Point B, return to Point C
- Sequential cycle numbering (#1, #2, #3...)
- Previous trip marked as "Auto Completed"

## 🔍 QR Code Standards

QR codes follow the format: `{location_id}:{timestamp}:{verification_hash}`

- **Client-side validation** before server submission
- **Fallback mechanisms** for poor lighting/camera conditions
- **Manual code entry** support
- **Consistent format** across all locations

## 📱 Mobile Optimization

- **Mobile-first design** approach
- **Touch-friendly controls** (minimum 44px touch targets)
- **One-handed operation** optimization
- **Responsive layouts** for all screen sizes
- **Offline mode handling** for poor connectivity

## 🔒 Security Features

- **JWT Authentication** with proper expiration handling
- **Parameterized queries** to prevent SQL injection
- **Rate limiting** on API endpoints
- **CORS configuration** for cross-origin requests
- **Helmet.js** for security headers
- **Audit logging** for critical operations

## 📈 Performance Standards

- **Database queries**: Complete within 500ms for dashboard operations
- **Mobile page loads**: Within 3 seconds on 3G connections
- **Connection pooling**: For database operations
- **Caching strategies**: For frequently accessed data

## 🧪 Testing

### Standard Test Suite
```bash
# Run all tests
npm test

# Server-specific tests
cd server && npm test
cd server && npm run test:coverage

# Client tests
cd client && npm test

# Integration tests
npm run test:integration
```

### Comprehensive System Testing

The system includes multiple comprehensive test scripts that validate different aspects of the shift management system:

#### Complete System Test
```bash
# Run complete system test
node scripts/test-complete-system.js
```

**What it tests:**
- ✅ Database function integrity (schedule_auto_activation)
- ✅ Current shift status display across all trucks
- ✅ Assignment display logic for Trip Monitoring
- ✅ Time context validation (day/night shift logic)
- ✅ Function signature verification
- ✅ Shift status statistics and counts

#### Fix Assignment Button Test
```bash
# Test the "Fix Assignment Display Issues" button functionality
node scripts/test-fix-assignment-button.js
```

**What it tests:**
- ✅ Button click simulation and server endpoint functionality
- ✅ Before/after shift status comparison
- ✅ Assignment display logic verification
- ✅ Server response validation
- ✅ Trip Monitoring display accuracy
- ✅ Function call success without signature errors

**Test Output Example:**
```
🔘 Testing "Fix Assignment Display Issues" Button Functionality...

1. Current Status BEFORE Fix:
   Total shifts: 4
   Active: 2
   Scheduled: 2
   Completed: 0

2. Simulating "Fix Assignment Display Issues" Button Click...
   ✅ Called schedule_auto_activation() function

3. Status AFTER Fix:
   Total shifts: 4
   Active: 2
   Scheduled: 2
   Completed: 0

4. Server Response (what the button would show):
   ✅ Success: true
   📝 Message: "Scheduled activation completed using corrected logic."

5. Assignment Display After Fix:
   Truck   | Driver          | Employee | Trip Monitoring Display
   --------|-----------------|----------|------------------------
   T001    | John Smith      | EMP001   | ✅ day Shift Active
   T002    | Jane Doe        | EMP002   | ✅ night Shift Active

🎯 BUTTON TEST RESULTS:
   Button Functionality: ✅ WORKING PERFECTLY
   Server Endpoint: ✅ Updated and functional
   Function Calls: ✅ No signature errors
   Assignment Display: ✅ Showing active shifts correctly
```

#### Complete Functionality Verification
```bash
# Run comprehensive functionality verification
node scripts/verify-complete-functionality.js
```

**Advanced testing features:**
- ✅ Function signature validation with parameter checking
- ✅ Shift status calculation vs expected status comparison
- ✅ Assignment display logic verification with detailed reporting
- ✅ Time context analysis with hour-by-hour validation
- ✅ Server endpoint integration testing
- ✅ Overall system health assessment with actionable recommendations

**Test Output Example:**
```
🔍 Comprehensive System Verification...

1. Testing Database Functions:
   ✅ schedule_auto_activation() function works
   ✅ Function signatures verified:
      evaluate_shift_status(shift_id integer, check_time timestamp without time zone)
      schedule_auto_activation()
      update_all_shift_statuses()

2. Current Shift Status Verification:
   Truck   | Driver        | Type  | Current   | Calculated | Expected  | Status
   --------|---------------|-------|-----------|------------|-----------|--------
   T001    | John Smith    | day   | active    | active     | ACTIVE    | ✅
   T002    | Jane Doe      | night | scheduled | scheduled  | SCHEDULED | ✅
   Overall Status Logic: ✅ CORRECT

3. Assignment Display Logic (Trip Monitoring):
   Assignment | Truck   | Driver          | Employee | Display Status
   -----------|---------|-----------------|----------|----------------
            1 | T001    | John Smith      | EMP001   | ✅ day Shift Active
            2 | T002    | Jane Doe        | EMP002   | 📅 night Shift Scheduled
   Trip Monitoring Status: ✅ ALL SHIFTS SHOWING CORRECTLY

🎯 VERIFICATION SUMMARY:
=====================================
   DATABASE FUNCTIONS    : ✅ Working
   SHIFT STATUS LOGIC    : ✅ Correct
   ASSIGNMENT DISPLAY    : ✅ Working
   SERVER ENDPOINT       : ✅ Updated
   FUNCTION SIGNATURES   : ✅ Fixed

🎉 OVERALL SYSTEM STATUS:
   ✅ FULLY OPERATIONAL
```

These comprehensive tests help diagnose shift management issues and validate that all components are working correctly together, providing detailed analysis and actionable recommendations for any issues found.

## 📚 Documentation

- **[System Health Monitoring Guide](docs/SYSTEM_HEALTH_MONITORING_GUIDE.md)** - Comprehensive monitoring and automated fixing for system health
- **[Shift Management System](docs/SHIFT_MANAGEMENT_SYSTEM.md)** - Comprehensive guide to automated shift management and overnight logic
- **[Appearance Settings Guide](docs/APPEARANCE_SETTINGS_GUIDE.md)** - Complete guide to customizing application appearance
- **[Assignment Shift Fix Guide](docs/ASSIGNMENT_SHIFT_FIX_GUIDE.md)** - Troubleshooting shift display issues
- **[Multi-Location Workflow Implementation](docs/MULTI_LOCATION_WORKFLOW_IMPLEMENTATION.md)** - Advanced workflow features
- **[API Documentation](docs/API_DOCUMENTATION.md)** - Complete API reference with shift management endpoints
- **[Architecture Decision Records](docs/adr/)** - Technical decisions and rationale

## 🚨 Troubleshooting

### Common Issues

#### Shift Display Problems
```bash
# Quick fix for shift cache issues
npm run fix:shift-cache

# Test the fix
npm run test:assignment-shift

# Final comprehensive shift status fix (now available)
node scripts/final-shift-status-fix.js
```

#### Database Connection Issues
1. Verify PostgreSQL is running
2. Check database credentials in `.env`
3. Ensure database exists and migrations are applied

#### QR Code Scanning Issues
1. Check camera permissions
2. Ensure adequate lighting
3. Use manual code entry as fallback
4. Verify QR code format

### Performance Issues
1. Check database query performance
2. Verify connection pooling configuration
3. Monitor WebSocket connections
4. Review caching strategies

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow the established project structure
- Write tests for new features
- Update documentation for API changes
- Test on mobile devices
- Maintain performance standards

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Target Users

- **Fleet managers and dispatchers** - Trip planning and monitoring
- **Drivers** - Mobile trip execution and QR scanning
- **Checkers** - Location verification and workflow management
- **Operations supervisors** - Performance monitoring and analytics
- **System administrators** - Configuration and maintenance

## 🎯 Business Value

- **Eliminates manual trip logging** and reduces paperwork
- **Provides real-time visibility** into fleet operations
- **Automates exception detection** and workflow management
- **Generates comprehensive analytics** and performance reports
- **Improves operational efficiency** and reduces errors
- **Enables data-driven decision making** for fleet optimization

---

For technical support or questions, please refer to the documentation in the `/docs` folder or contact the development team.