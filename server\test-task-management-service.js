/**
 * Task Management Service Test Script
 * 
 * This script tests the functionality of the TaskManagementService
 * and the task management API endpoints.
 */

const TaskManagementService = require('./services/TaskManagementService');

// Initialize services
const taskService = new TaskManagementService();

// Test function to run all tests
async function runTests() {
  console.log('=== Task Management Service Tests ===');
  
  try {
    // Test task creation
    console.log('\nTesting task creation...');
    const testTask = await taskService.createTask({
      type: 'test',
      priority: 'medium',
      title: 'Test Task',
      description: 'This is a test task created by the test script',
      autoExecutable: true,
      metadata: {
        test: true,
        createdBy: 'test-script'
      }
    });
    console.log('✅ Task created successfully:', testTask.id);
    
    // Test getting tasks
    console.log('\nTesting task retrieval...');
    const tasks = await taskService.getTasks({ type: 'test' }, 10);
    console.log(`✅ Retrieved ${tasks.length} tasks`);
    
    // Test getting task by ID
    console.log('\nTesting task retrieval by ID...');
    const task = await taskService.getTaskById(testTask.id);
    console.log('✅ Retrieved task by ID:', task.id);
    
    // Test updating task status
    console.log('\nTesting task status update...');
    const updatedTask = await taskService.updateTaskStatus(testTask.id, 'in_progress', {
      description: 'Updated description'
    });
    console.log('✅ Task status updated to:', updatedTask.status);
    
    // Test system health logging
    console.log('\nTesting system health logging...');
    const healthLog = await taskService.logSystemHealth(
      'test_module',
      'operational',
      [{ type: 'test_issue', message: 'Test issue' }],
      { performance: 100, memory: 50 }
    );
    console.log('✅ System health logged successfully:', healthLog.id);
    
    // Test getting health logs
    console.log('\nTesting health log retrieval...');
    const healthLogs = await taskService.getSystemHealthLogs('test_module', 5);
    console.log(`✅ Retrieved ${healthLogs.length} health logs`);
    
    // Test recommendation generation
    console.log('\nTesting recommendation generation...');
    const systemHealth = {
      shifts: {
        status: 'warning',
        issues: [{ type: 'shift_status_mismatch', message: 'Shift status mismatch' }]
      },
      assignments: {
        status: 'operational',
        issues: []
      },
      trips: {
        status: 'critical',
        issues: [
          { type: 'trip_workflow_error', message: 'Trip workflow error' },
          { type: 'driver_assignment_mismatch', message: 'Driver assignment mismatch' }
        ]
      }
    };
    
    const recommendations = await taskService.generateRecommendations(systemHealth);
    console.log(`✅ Generated ${recommendations.length} recommendations`);
    
    // Test task execution
    console.log('\nTesting task execution...');
    const executionResult = await taskService.executeTask(updatedTask);
    console.log('✅ Task executed successfully:', executionResult.status);
    
    // Test task deletion
    console.log('\nTesting task deletion...');
    const deleteResult = await taskService.deleteTask(testTask.id);
    console.log('✅ Task deleted successfully:', deleteResult);
    
    console.log('\n=== All tests completed successfully ===');
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

// Run tests
runTests()
  .then(() => {
    console.log('\nTests completed, exiting...');
    process.exit(0);
  })
  .catch(error => {
    console.error('Error running tests:', error);
    process.exit(1);
  });